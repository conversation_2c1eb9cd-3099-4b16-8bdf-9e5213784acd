<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <title>Example</title>
    <style type="text/css" media="screen">
        body {
            overflow: hidden;
            width: 100%;
            height: 100%;
            margin: 0;
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
        }
        #iframeEditor {
            margin: 0;
            position: absolute;
            top: 0;
            bottom: 0;
            left: 30%;
            right: 0;
        }
        #editorLog {
            margin: 0;
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            right: 70%;
            overflow: hidden;

            border-right-color: #CBCBCB;
            border-right-style: solid;
            border-right-width: 1px;

            padding: 10px;
        }
    </style>
</head>
<body>
    <div id="editorLog"></div>
    <div id="iframeEditor"></div>
<script type="text/javascript" src="AceEditor.js"></script>
<script type="text/javascript" language="javascript">

    var aceEditor;

    var onChangeValue = function (event) {
        document.getElementById('editorLog').innerText = event.data ? event.data.value : '';
        console.log("onChangeValue at position(" + event.data.pos.row + ", " + event.data.pos.column + ")");
    };

    var onEditorReady = function (event) {
        console.log("onEditorReady");
        aceEditor.setValue('//Welcome!\n');
        aceEditor.disableDrop(true);
        aceEditor.updateTheme('dark' /* 'dark' or 'light' */, {
//                                        'text-normal': '', 'icon-normal': '', 'background-normal': '', 'background-toolbar': '', 'highlight-button-hover': '',
//                                        'canvas-background': '', 'border-divider': '', 'canvas-scroll-thumb-pressed': '', 'canvas-scroll-thumb': ''
                                      } // theme colors (optional)
                             );
    };

    var onLoad = function (event) {
        console.log("onLoad");
    };

    var config = {
        editorType: 'word',
        events: {
            onChangeValue: onChangeValue,
            onEditorReady: onEditorReady,
            onLoad: onLoad
        }
    };

    aceEditor = new window.AceEditor("iframeEditor", config);
</script>
</body>
</html>
