<p align="center"><img src="http://lesscss.org/public/img/less_logo.png" width="264" height="117">
    
<p align="center">
    <a href="https://github.com/less/less.js/actions?query=branch%3Amaster"><img src="https://github.com/less/less.js/actions/workflows/ci.yml/badge.svg?branch=master" alt="Github Actions CI"/></a>
    <a href="https://www.npmtrends.com/less"><img src="https://img.shields.io/npm/dm/less.svg?sanitize=true" alt="Downloads"></a>
    <a href="https://www.npmjs.com/package/less"><img src="https://img.shields.io/npm/v/less.svg?sanitize=true" /></a>
    <a href="https://twitter.com/lesstocss"><img alt="Twitter Follow" src="https://img.shields.io/twitter/follow/lesstocss.svg?style=flat-square" style="max-width:100%;" /></a>
</p>

<p align="center"><a href="https://gitter.im/less/less.js?utm_source=badge&amp;utm_medium=badge&amp;utm_campaign=pr-badge&amp;utm_content=badge"><img src="https://badges.gitter.im/Join%20Chat.svg" style="max-width:100%;"></a> <br><sup class="rich-diff-level-one">Chat with Less.js users and contributors</sup></p>

This is the Less.js monorepo, managed via [Lerna](https://lerna.js.org/).

## More information

For general information on the language, configuration options or usage visit [lesscss.org](http://lesscss.org).

Here are other resources for using Less.js:

* [stackoverflow.com][so] is a great place to get answers about Less.
* [Less.js Issues][issues] for reporting bugs


## Contributing
Please read [CONTRIBUTING.md](CONTRIBUTING.md). Add unit tests for any new or changed functionality. Lint and test your code using [Grunt](http://gruntjs.com).

### Reporting Issues

Before opening any issue, please search for existing issues and read the [Issue Guidelines](https://github.com/necolas/issue-guidelines), written by [Nicolas Gallagher](https://github.com/necolas). After that if you find a bug or would like to make feature request, [please open a new issue][issues].

Please report documentation issues in [the documentation project](https://github.com/less/less-docs).

### Development

Read [Developing Less](http://lesscss.org/usage/#developing-less).

## Release History
See the [changelog](CHANGELOG.md)

## Contributors

### Code Contributors

This project exists thanks to all the people who contribute. [[Contribute](CONTRIBUTING.md)].
<a href="https://github.com/less/less.js/graphs/contributors"><img src="https://opencollective.com/less/contributors.svg?width=890&button=false" /></a>


## [License](LICENSE)

Copyright (c) 2009-2017 [Alexis Sellier](http://cloudhead.io) & The Core Less Team
Licensed under the [Apache License](LICENSE).


[so]: http://stackoverflow.com/questions/tagged/less "StackOverflow.com"
[issues]: https://github.com/less/less.js/issues "GitHub Issues for Less.js"
[download]: https://github.com/less/less.js/zipball/master "Download Less.js"
