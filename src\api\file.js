/**
 * 文件操作相关API - 对接 disk 接口
 */

import { get, post, put, del, upload } from '@/utils/request';

/**
 * 获取文件列表
 * @param {Object} params 请求参数
 * @param {string} params.sort 排序字段
 * @param {string} params.order 排序方式 asc/desc
 * @param {string} params.type 文件类型筛选
 * @param {string} params.search 搜索关键词
 * @returns {Promise} 请求结果
 */
export const getFileList = (params) => {
  return get('/api/v1/disk/files', params);
};


/**
 * 下载单个文件
 * @param {string} fileId 文件ID
 * @param {Function} onProgress 进度回调
 * @returns {Promise} 请求结果
 */
export const downloadFile = (fileId, onProgress) => {
  return get(`/api/v1/disk/download/${fileId}`, {}, {
    responseType: 'blob',
    onDownloadProgress: (progressEvent) => {
      if (onProgress && progressEvent.total) {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        onProgress(progress);
      }
    }
  });
};

/**
 * 批量下载文件（打包为zip）
 * @param {Array} fileIds 文件ID数组
 * @param {Function} onProgress 进度回调
 * @returns {Promise} 请求结果
 */
export const downloadFiles = (fileIds, onProgress) => {
  return post('/api/v1/disk/download', { file_ids: fileIds }, {
    responseType: 'blob',
    onDownloadProgress: (progressEvent) => {
      if (onProgress && progressEvent.total) {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        onProgress(progress);
      }
    }
  });
};

/**
 * 分享文件
 * @param {Object} params 分享参数
 * @param {string} params.file_id 文件ID
 * @param {number} params.expire_days 过期天数
 * @param {boolean} params.allow_download 是否允许下载
 * @returns {Promise} 请求结果
 */
export const shareFile = (params) => {
  return post('/api/v1/disk/share', params);
};

/**
 * 重命名文件
 * @param {Object} params 重命名参数
 * @param {string} params.file_id 文件ID
 * @param {string} params.new_name 新文件名
 * @returns {Promise} 请求结果
 */
export const renameFile = (params) => {
  return post('/api/v1/disk/rename', params);
};

/**
 * 复制文件
 * @param {Object} params 复制参数
 * @param {Array} params.file_ids 文件ID数组
 * @param {string} params.target_path 目标路径
 * @returns {Promise} 请求结果
 */
export const copyFiles = (params) => {
  return post('/api/v1/disk/copy', params);
};

/**
 * 移动文件
 * @param {Object} params 移动参数
 * @param {Array} params.file_ids 文件ID数组
 * @param {string} params.target_path 目标路径
 * @returns {Promise} 请求结果
 */
export const moveFiles = (params) => {
  return post('/api/v1/disk/move', params);
};

/**
 * 删除文件（移动到回收站）
 * @param {Array} fileIds 文件ID数组
 * @returns {Promise} 请求结果
 */
export const deleteFiles = (fileIds) => {
  return post('/api/v1/disk/trash', { file_ids: fileIds });
};

/**
 * 创建文件夹
 * @param {Object} params 文件夹参数
 * @param {string} params.folder_name 文件夹名称
 * @param {string} params.parent_id 父级id
 * @returns {Promise} 请求结果
 */
export const createFolder = (params) => {
  return post('/api/v1/disk/folder', params);
};

/**
 * 创建文档
 * @param {Object} params 文档参数
 * @param {string} params.name 文档名称
 * @param {string} params.file_type 文档类型
 * @param {string} params.parent_path 父级路径
 * @returns {Promise} 请求结果
 */
export const createDocument = (params) => {
  return post('/api/v1/disk/file', params);
};

/**
 * 上传文件
 * @param {Object} params 上传参数
 * @param {string} params.folder_id 文件夹ID
 * @param {File} params.file 文件对象
 * @param {string} params.file_name 文件名
 * @param {Function} onProgress 进度回调
 * @returns {Promise} 请求结果
 */
export const uploadFile = (params, onProgress) => {
  const formData = new FormData();
  formData.append('folder_id', params.folder_id);
  formData.append('file', params.file);
  formData.append('file_name', params.file_name);

  return upload('/api/v1/disk/upload', formData, {
    onUploadProgress: (progressEvent) => {
      if (onProgress && progressEvent.total) {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        onProgress(progress);
      }
    }
  });
};


/**
 * 切换文件收藏状态
 * @param {string} fileId 文件ID
 * @param {boolean} starred 是否收藏
 * @returns {Promise} 请求结果
 */
export const toggleFileStarred = (fileId, starred) => {
  if (starred) {
    return post(`/api/v1/disk/star/${fileId}`);
  } else {
    return del(`/api/v1/disk/star/${fileId}`);
  }
};

/**
 * 获取收藏的文件列表
 * @param {Object} params 请求参数
 * @returns {Promise} 请求结果
 */
export const getStarredFiles = (params = {}) => {
  return get('/api/v1/disk/starred', params);
};

/**
 * 获取最近访问的文件列表
 * @param {Object} params 请求参数
 * @returns {Promise} 请求结果
 */
export const getRecentFiles = (params = {}) => {
  return get('/api/v1/disk/recent', params);
};

/**
 * 获取回收站文件列表
 * @param {Object} params 请求参数
 * @returns {Promise} 请求结果
 */
export const getTrashFiles = (params = {}) => {
  return get('/api/v1/disk/trash', params);
};

/**
 * 从回收站恢复文件
 * @param {string} fileId 文件ID
 * @returns {Promise} 请求结果
 */
export const restoreFromTrash = (fileId) => {
  return post(`/api/v1/disk/trash/restore/${fileId}`);
};

/**
 * 永久删除回收站中的文件
 * @param {string} fileId 文件ID
 * @returns {Promise} 请求结果
 */
export const permanentDeleteFile = (fileId) => {
  return del(`/api/v1/disk/trash/${fileId}`);
};

/**
 * 清空回收站
 * @returns {Promise} 请求结果
 */
export const emptyTrash = () => {
  return post('/api/v1/disk/trash/empty');
};

/**
 * 获取共享文件列表
 * @param {Object} params 请求参数
 * @returns {Promise} 请求结果
 */
export const getSharedFiles = (params = {}) => {
  return get('/api/v1/disk/shared', params);
};

/**
 * 获取文件标签列表
 * @returns {Promise} 请求结果
 */
export const getFileTags = () => {
  return get('/api/v1/disk/tags');
};

/**
 * 获取标签列表（别名，用于统计信息）
 * @returns {Promise} 请求结果
 */
export const getTags = () => {
  return getFileTags();
};

/**
 * 获取用户统计信息
 * @returns {Promise} 请求结果
 */
export const getUserStats = () => {
  return get('/api/v1/stats/user');
};

/**
 * 创建文件标签
 * @param {Object} tagData 标签数据
 * @param {string} tagData.name 标签名称
 * @param {string} tagData.color 标签颜色
 * @returns {Promise} 请求结果
 */
export const createFileTag = (tagData) => {
  return post('/api/v1/disk/tags', tagData);
};

/**
 * 删除文件标签
 * @param {string} tagId 标签ID
 * @returns {Promise} 请求结果
 */
export const deleteFileTag = (tagId) => {
  return del(`/api/v1/disk/tags/${tagId}`);
};

/**
 * 为文件添加标签
 * @param {string} fileId 文件ID
 * @param {string} tagId 标签ID
 * @returns {Promise} 请求结果
 */
export const addFileTag = (fileId, tagId) => {
  return post('/api/v1/disk/file/tag', {
    file_id: fileId,
    tag_id: tagId
  });
};

/**
 * 移除文件标签
 * @param {string} fileId 文件ID
 * @param {string} tagId 标签ID
 * @returns {Promise} 请求结果
 */
export const removeFileTag = (fileId, tagId) => {
  return del(`/api/v1/disk/file/${fileId}/tag/${tagId}`);
};

/**
 * 根据标签获取文件列表
 * @param {string} tagId 标签ID
 * @param {Object} params 请求参数
 * @returns {Promise} 请求结果
 */
export const getFilesByTag = (tagId, params = {}) => {
  return get(`/v1/disk/tag/${tagId}/files`, params);
};

/**
 * 获取文件详情
 * @param {string} fileId 文件ID
 * @returns {Promise} 请求结果
 */
export const getFileDetail = (fileId) => {
  return get(`/api/v1/disk/file/${fileId}`);
};

/**
 * 获取文件夹详情
 * @param {string} folderId 文件夹ID
 * @returns {Promise} 请求结果
 */
export const getFolderDetails = (folderId) => {
  return get(`/api/v1/disk/folder/${folderId}`);
};

/**
 * 团队文件相关接口
 */

/**
 * 获取团队文件列表
 * @param {string} teamId 团队ID
 * @param {Object} params 请求参数
 * @returns {Promise} 请求结果
 */
export const getTeamFiles = (teamId, params = {}) => {
  return get(`/api/v1/team/${teamId}/files`, params);
};

/**
 * 移动文件到团队
 * @param {string} teamId 团队ID
 * @param {Object} params 移动参数
 * @returns {Promise} 请求结果
 */
export const moveFileToTeam = (teamId, params) => {
  return post(`/api/v1/team/${teamId}/file/move`, params);
};