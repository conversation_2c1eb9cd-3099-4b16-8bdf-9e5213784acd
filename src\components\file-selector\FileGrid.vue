<template>
  <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
    <template v-for="file in files" :key="file.id">
      <!-- 文件夹组件 -->
      <FolderBox
        v-if="file.type === 'folder'"
        :folder="file"
        :file-colors="fileColors"
        :dropdown-options="dropdownOptions"
        :is-multi-select-mode="isMultiSelectMode"
        :is-selected="isFileSelected(file)"
        @click="handleFileClick"
        @toggle-starred="$emit('toggle-starred', $event)"
        @dropdown-select="(key, folder) => $emit('dropdown-select', key, folder)"
        @checkbox-click="handleCheckboxClick"
        @context-menu="handleContextMenu"
      />

      <!-- 文件组件 -->
      <FileBox
        v-else
        :file="file"
        :file-icons="fileIcons"
        :file-colors="fileColors"
        :dropdown-options="dropdownOptions"
        :is-multi-select-mode="isMultiSelectMode"
        :is-selected="isFileSelected(file)"
        @click="handleFileClick"
        @toggle-starred="$emit('toggle-starred', $event)"
        @dropdown-select="(key, file) => $emit('dropdown-select', key, file)"
        @checkbox-click="handleCheckboxClick"
        @context-menu="handleContextMenu"
      />
    </template>
  </div>
</template>

<script setup>
import { watch } from 'vue';
import FolderBox from './FolderBox.vue';
import FileBox from './FileBox.vue';

const props = defineProps({
  files: {
    type: Array,
    default: () => []
  },
  fileIcons: {
    type: Object,
    required: true
  },
  fileColors: {
    type: Object,
    required: true
  },
  dropdownOptions: {
    type: Array,
    default: () => []
  },
  isMultiSelectMode: {
    type: Boolean,
    default: false
  },
  selectedFiles: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['select-file', 'toggle-starred', 'dropdown-select', 'select-multiple']);

// 检查文件是否被选中
const isFileSelected = (file) => {
  return props.selectedFiles.some(f => f.id === file.id);
};

// 处理文件点击
const handleFileClick = (event, file) => {
  console.log('file',file)
  emit('select-file', file, event);
};

// 处理复选框点击
const handleCheckboxClick = (file) => {
  emit('select-multiple', file);
};

// 处理右键菜单
const handleContextMenu = (event, _file) => {
  event.preventDefault();
  // 这里可以添加右键菜单逻辑
};


watch(()=>props.files,(val)=>{
  console.log(val,'val')
},{
  immediate:true
})
</script>