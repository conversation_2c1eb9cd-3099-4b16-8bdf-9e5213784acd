# Security Policy

## Supported Versions

We currently support the following versions of Backbone with security updates:

- the latest commit on the `master` branch (published as "edge" on the
  [project website][website]);
- the 1.x release tagged as [latest][npm-latest] on npm;
- any release tagged as [preview][npm-preview] on npm, if present.

[website]: https://backbonejs.org
[npm-latest]: https://www.npmjs.com/package/backbone/v/latest
[npm-preview]: https://www.npmjs.com/package/backbone/v/preview

## Reporting a Vulnerability

Please report security issues by sending an email to
dev@juliangonggrijp.<NAME_EMAIL>.

Do __not__ submit an issue ticket or pull request or otherwise publicly
disclose the issue.

After receiving your email, we will respond as soon as possible and indicate
what we plan to do.

## Disclosure policy

After confirming a vulnerability, we will generally release a security update
as soon as possible, including the minimum amount of information required for
software maintainers and system administrators to assess the urgency of the
update for their particular situation.

We postpone the publication of any further details such as code comments,
tests, commit history and diffs, in order to enable a substantial share of the
users to install the security fix before this time.

Upon publication of full details, we will credit the reporter if the reporter wishes to be publicly identified.
