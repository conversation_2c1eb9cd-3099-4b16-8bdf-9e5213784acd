<template>
  <div class="file-breadcrumb" :style="{
    backgroundColor: themeColors.bgPrimary,
    borderColor: themeColors.borderColor
  }">
    <div class="breadcrumb-content">
      <template v-for="(path, index) in currentPath" :key="index">
        <span 
          v-if="index < currentPath.length - 1" 
          class="breadcrumb-item clickable"
          :style="{ color: themeColors.textSecondary }"
          @click="$emit('navigate', index)"
        >
          {{ path.name }}
        </span>
        <span 
          v-else 
          class="breadcrumb-item current"
          :style="{ color: themeColors.textColor }"
        >
          {{ path.name }}
        </span>
        
        <span 
          v-if="index < currentPath.length - 1" 
          class="breadcrumb-separator"
          :style="{ color: themeColors.textTertiary }"
        >
          /
        </span>
      </template>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useThemeStore } from '../../stores/theme';

const props = defineProps({
  currentPath: {
    type: Array,
    required: true,
    default: () => [{
      name: '全部文件',
      id: 0
    }]
  }
});

defineEmits(['navigate']);

const themeStore = useThemeStore();
const themeColors = computed(() => themeStore.getThemeColors());
</script>

<style scoped lang="scss">
.file-breadcrumb {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  margin-bottom: 16px;
  border-radius: 8px;
  border: 1px solid;
  background-color: var(--file-bg);
  transition: all 0.3s ease;
  font-size: 14px;
  min-height: 44px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.breadcrumb-content {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 4px;
  width: 100%;
}

.breadcrumb-item {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
  white-space: nowrap;
  
  &.clickable {
    cursor: pointer;
    
    &:hover {
      background-color: var(--file-bg-secondary);
      color: var(--theme-primary) !important;
      transform: translateY(-1px);
    }
    
    &:active {
      transform: translateY(0);
    }
  }
  
  &.current {
    font-weight: 600;
    background-color: var(--file-bg-secondary);
    border: 1px solid var(--theme-border-secondary);
  }
}

.breadcrumb-separator {
  margin: 0 2px;
  font-weight: 300;
  user-select: none;
}

// 响应式优化
@media (max-width: 768px) {
  .file-breadcrumb {
    padding: 10px 12px;
    font-size: 13px;
    margin-bottom: 12px;
  }
  
  .breadcrumb-item {
    padding: 3px 6px;
  }
}

// 暗色主题优化
@media (prefers-color-scheme: dark) {
  .file-breadcrumb {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  }
}
</style>
