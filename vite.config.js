import vue from "@vitejs/plugin-vue";
import path from "path";
import { defineConfig, loadEnv, searchForWorkspaceRoot } from "vite";
import VueDevTools from 'vite-plugin-vue-devtools';

const sdkjsPath = path.resolve(__dirname, "../sdkjs");

// https://vitejs.dev/config/
export default ({ mode }) => {
  process.env = { ...process.env, ...loadEnv(mode, process.cwd()) };
  return defineConfig({
    plugins: [VueDevTools(),vue()],
    build: {
      watch: {
        // 监控文件变化
        include: ["src/**/*"],
      },
    },
    server: {
      host: '0.0.0.0', // 监听所有网络接口，允许局域网访问
      fs: {
        // 允许访问项目根目录之外的文件
        allow: [searchForWorkspaceRoot(process.cwd()), sdkjsPath],
      },
      proxy: {
        // 将 /sdkjs 的请求代理到项目根目录的上一级目录
        // 例如，请求 /sdkjs/word/word.js -> 将在 ../sdkjs/word/word.js 中查找文件
        "/sdkjs": {
          target: `http://localhost:${process.env.VITE_PORT || 5173}`,
          // target:'file://'+ path.resolve(__dirname, "../sdkjs"),
          changeOrigin: true,
          rewrite: (p) =>
            `/@fs/${path.join(sdkjsPath, p.replace(/^\/sdkjs/, ""))}`,
        },
        "/doc": {
          target: "ws://localhost:8080",

          // 关键：必须设置为 true，启用 WebSocket 代理
          ws: true,

          // 建议开启，以确保请求头中的 Host 字段被正确设置为目标地址
          changeOrigin: true,
        },
        // 添加API代理，解决CORS问题
        "/api": {
          target: "http://*************:8890",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, ""),
        },
      },
    },
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
        // 'sdkjs/': path.resolve(__dirname, "../sdkjs/"),
        // 'sdkjs' alias is no longer needed as we are using a proxy
      },
    },
  });
};
