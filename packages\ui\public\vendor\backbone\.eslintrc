{"env": {"browser": true, "node": true, "amd": true}, "globals": {"attachEvent": false, "detachEvent": false}, "rules": {"array-bracket-spacing": 2, "block-scoped-var": 2, "brace-style": [1, "1tbs", {"allowSingleLine": true}], "camelcase": 2, "comma-dangle": [2, "never"], "comma-spacing": 2, "computed-property-spacing": [2, "never"], "dot-notation": [2, {"allowKeywords": false}], "eol-last": 2, "eqeqeq": [2, "smart"], "indent": [2, 2, {"MemberExpression": 0, "SwitchCase": 1, "VariableDeclarator": 2}], "key-spacing": 1, "keyword-spacing": [2, {"after": true}], "linebreak-style": 2, "max-depth": [1, 4], "max-params": [1, 5], "new-cap": [2, {"newIsCapExceptions": ["model"]}], "no-alert": 2, "no-caller": 2, "no-catch-shadow": 2, "no-console": 2, "no-debugger": 2, "no-delete-var": 2, "no-div-regex": 1, "no-dupe-args": 2, "no-dupe-keys": 2, "no-duplicate-case": 2, "no-else-return": 1, "no-empty-character-class": 2, "no-eval": 2, "no-ex-assign": 2, "no-extend-native": 2, "no-extra-boolean-cast": 2, "no-extra-parens": 1, "no-extra-semi": 2, "no-fallthrough": 2, "no-floating-decimal": 2, "no-func-assign": 2, "no-implied-eval": 2, "no-inner-declarations": 2, "no-irregular-whitespace": 2, "no-label-var": 2, "no-labels": 2, "no-lone-blocks": 2, "no-lonely-if": 2, "no-multi-str": 2, "no-native-reassign": 2, "no-negated-in-lhs": 1, "no-new-object": 2, "no-new-wrappers": 2, "no-obj-calls": 2, "no-octal": 2, "no-octal-escape": 2, "no-proto": 2, "no-redeclare": 2, "no-shadow": 2, "no-spaced-func": 2, "no-throw-literal": 2, "no-trailing-spaces": 2, "no-undef": 2, "no-undef-init": 2, "no-undefined": 2, "no-unneeded-ternary": 2, "no-unreachable": 2, "no-unused-expressions": [2, {"allowTernary": true, "allowShortCircuit": true}], "no-with": 2, "object-curly-spacing": [2, "never"], "quote-props": [1, "consistent-as-needed", {"keywords": true}], "quotes": [2, "single", "avoid-escape"], "radix": 2, "semi": 2, "space-before-function-paren": [2, {"anonymous": "never", "named": "never"}], "space-infix-ops": 2, "space-unary-ops": [2, {"words": true, "nonwords": false}], "use-isnan": 2, "valid-typeof": 2, "wrap-iife": [2, "inside"]}}