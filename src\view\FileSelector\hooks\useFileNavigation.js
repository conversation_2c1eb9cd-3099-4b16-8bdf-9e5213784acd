import { useMessage } from 'naive-ui';
import * as fileApi from '@/api/file.js';

/**
 * 文件导航 Hook
 * 处理文件夹导航、路径管理等
 */
export function useFileNavigation() {
  const message = useMessage();

    // 导航到指定路径
    const navigateToPath = async (index, { currentPath,loadFileList }) => {

      if (index >= currentPath.length - 1) return;
    
      try {
        // 截取路径到指定索引
        currentPath.splice(index + 1);

        // 获取目标文件夹ID（如果是根目录则为null）
        const targetFolder = currentPath[currentPath.length - 1];
        const folderId = targetFolder?.id || null;
        
        // 加载文件列表
        await loadFileList({ folder_id: folderId });
        
      } catch (error) {
        console.error('导航失败:', error);
      }
  };

  // 导航到文件夹
  const navigateToFolder = async (folder, { currentPath, loadFileList }) => {
      currentPath.push({
        name: folder.name,
        id: folder.id
      });
      await loadFileList({
        folder_id: folder.id
      });
  };
  // 返回上级目录
  const goBack = async ({ currentPath, loadFileList }) => {
    if (currentPath.length > 1) {
      currentPath.pop();
      await loadFileList({
        folder_id: currentPath.at(-1)?.id || null
      });
    }
  };

  return {
    navigateToPath,
    navigateToFolder,
    goBack
  };
}