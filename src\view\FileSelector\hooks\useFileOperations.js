import { useMessage } from 'naive-ui';
import * as fileApi from '@/api/file.js';

/**
 * 文件操作 Hook
 * 处理文件的基本操作：下载、分享、重命名、复制、移动、删除、创建等
 */
export function useFileOperations() {
  const message = useMessage();

  // 下载文件
  const handleDownloadFile = async (file, { loadFileList, loadSidebarStats }) => {
    try {
      const response = await fileApi.downloadFile(file.id);
      if (response.isSuccess) {
        const url = window.URL.createObjectURL(response.blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = file.name;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
        message.success(`文件 ${file.name} 下载成功`);
      } else {
        message.error(`下载失败: ${response.message}`);
      }
    } catch (error) {
      console.error('下载文件出错:', error);
      message.error('下载失败，请稍后重试');
    }
  };

  // 分享文件
  const handleShareFile = async (file, { loadFileList, loadSidebarStats }) => {
    try {
      const response = await fileApi.shareFile(file.id);
      if (response.isSuccess) {
        message.success(`文件 ${file.name} 分享成功`);
        await loadSidebarStats();
      } else {
        message.error(`分享失败: ${response.message}`);
      }
    } catch (error) {
      console.error('分享文件出错:', error);
      message.error('分享失败，请稍后重试');
    }
  };

  // 重命名文件
  const handleRenameFile = async (file, { loadFileList, loadSidebarStats }) => {
    const newName = prompt('请输入新的文件名:', file.name);
    if (newName && newName !== file.name) {
      try {
        const response = await fileApi.renameFile(file.id, newName);
        if (response.isSuccess) {
          message.success(`文件重命名成功`);
          await loadFileList();
          await loadSidebarStats();
        } else {
          message.error(`重命名失败: ${response.message}`);
        }
      } catch (error) {
        console.error('重命名文件出错:', error);
        message.error('重命名失败，请稍后重试');
      }
    }
  };

  // 复制文件
  const handleCopyFile = async (file, { loadFileList, loadSidebarStats }) => {
    try {
      const response = await fileApi.copyFile(file.id);
      if (response.isSuccess) {
        message.success(`文件 ${file.name} 复制成功`);
        await loadFileList();
        await loadSidebarStats();
      } else {
        message.error(`复制失败: ${response.message}`);
      }
    } catch (error) {
      console.error('复制文件出错:', error);
      message.error('复制失败，请稍后重试');
    }
  };

  // 移动文件
  const handleMoveFile = async (file, { loadFileList, loadSidebarStats }) => {
    // 这里应该打开文件夹选择器，暂时用 prompt 代替
    const targetPath = prompt('请输入目标路径:', '/');
    if (targetPath) {
      try {
        const response = await fileApi.moveFile(file.id, targetPath);
        if (response.isSuccess) {
          message.success(`文件 ${file.name} 移动成功`);
          await loadFileList();
          await loadSidebarStats();
        } else {
          message.error(`移动失败: ${response.message}`);
        }
      } catch (error) {
        console.error('移动文件出错:', error);
        message.error('移动失败，请稍后重试');
      }
    }
  };

  // 删除文件
  const handleDeleteFile = async (file, { loadFileList, loadSidebarStats }) => {
    if (confirm(`确定要删除文件 "${file.name}" 吗？`)) {
      try {
        const response = await fileApi.deleteFile(file.id);
        if (response.isSuccess) {
          message.success(`文件 ${file.name} 已删除`);
          await loadFileList();
          await loadSidebarStats();
        } else {
          message.error(`删除失败: ${response.message}`);
        }
      } catch (error) {
        console.error('删除文件出错:', error);
        message.error('删除失败，请稍后重试');
      }
    }
  };

  // 创建文件夹
  const handleCreateFolder = async (name, { currentPath, loadFileList, loadSidebarStats }) => {
    try {
      const response = await fileApi.createFolder(name, currentPath.value.at(-1).id);
      if (response.isSuccess) {
        message.success(`文件夹 ${name} 创建成功`);
        await loadFileList();
        await loadSidebarStats();
      } else {
        message.error(`创建失败: ${response.message}`);
      }
    } catch (error) {
      console.error('创建文件夹出错:', error);
      message.error('创建失败，请稍后重试');
    }
  };

  // 创建文档
  const handleCreateDocument = async (name, type, { currentPath, loadFileList, loadSidebarStats }) => {
    try {
      const response = await fileApi.createDocument(name, type, currentPath.value.at(-1).id);
      if (response.isSuccess) {
        message.success(`文档 ${name} 创建成功`);
        await loadFileList();
        await loadSidebarStats();
      } else {
        message.error(`创建失败: ${response.message}`);
      }
    } catch (error) {
      console.error('创建文档出错:', error);
      message.error('创建失败，请稍后重试');
    }
  };

  // 创建确认处理
  const handleCreateConfirm = async (data, { currentPath, loadFileList, loadSidebarStats }) => {
    if (data.type === 'folder') {
      await handleCreateFolder(data.name, { currentPath, loadFileList, loadSidebarStats });
    } else {
      await handleCreateDocument(data.name, data.docType, { currentPath, loadFileList, loadSidebarStats });
    }
  };

  return {
    handleDownloadFile,
    handleShareFile,
    handleRenameFile,
    handleCopyFile,
    handleMoveFile,
    handleDeleteFile,
    handleCreateFolder,
    handleCreateDocument,
    handleCreateConfirm
  };
}