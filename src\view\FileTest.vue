<template>
  <div class="file-test-container p-6">
    <h1 class="text-2xl font-bold mb-6">文件显示测试</h1>
    
    <!-- 测试数据显示 -->
    <div class="mb-6">
      <h2 class="text-lg font-semibold mb-4">测试文件数据：</h2>
      <pre class="bg-gray-100 p-4 rounded text-sm overflow-auto">{{ JSON.stringify(testFile, null, 2) }}</pre>
    </div>

    <!-- FileBox 组件测试 -->
    <div class="mb-6">
      <h2 class="text-lg font-semibold mb-4">FileBox 组件显示：</h2>
      <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        <FileBox
          :file="testFile"
          :file-icons="fileIcons"
          :file-colors="fileColors"
          :dropdown-options="dropdownOptions"
          :is-multi-select-mode="false"
          :is-selected="false"
          @click="handleFileClick"
          @toggle-starred="handleToggleStarred"
          @dropdown-select="handleDropdownSelect"
        />
      </div>
    </div>

    <!-- 文件类型测试 -->
    <div class="mb-6">
      <h2 class="text-lg font-semibold mb-4">文件类型识别测试：</h2>
      <div class="space-y-2">
        <div v-for="filename in testFilenames" :key="filename" class="flex justify-between items-center p-2 bg-gray-50 rounded">
          <span>{{ filename }}</span>
          <span class="font-mono text-sm">{{ getFileType(filename) }}</span>
        </div>
      </div>
    </div>

    <!-- 图标颜色配置显示 -->
    <div class="mb-6">
      <h2 class="text-lg font-semibold mb-4">图标和颜色配置：</h2>
      <div class="grid grid-cols-2 gap-4">
        <div>
          <h3 class="font-medium mb-2">文件图标：</h3>
          <pre class="bg-gray-100 p-2 rounded text-xs">{{ JSON.stringify(fileIcons, null, 2) }}</pre>
        </div>
        <div>
          <h3 class="font-medium mb-2">文件颜色：</h3>
          <pre class="bg-gray-100 p-2 rounded text-xs">{{ JSON.stringify(fileColors, null, 2) }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import FileBox from '@/components/file-selector/FileBox.vue';
import { getFileType } from '@/utils/fileUtils.js';

// 测试文件数据 - 使用您提供的数据结构
const testFile = ref({
  file_id: "file_d26596djr2n1nh1pd1f0",
  file_name: "毕业论文1.docx",
  file_size: 821669,
  file_type: "docx",
  upload_time: "2025-08-01 05:46:01",
  update_time: "2025-08-01 05:46:01",
  folder_id: "0",
  is_starred: false,
  tags: null,
  status: 1,
  // 映射后的字段
  id: "file_d26596djr2n1nh1pd1f0",
  name: "毕业论文1.docx",
  type: "doc", // 使用 getFileType 函数的结果，docx -> doc
  size: 821669,
  modifiedAt: "2025-08-01 05:46:01",
  createdAt: "2025-08-01 05:46:01",
  starred: false
});

// 测试文件名列表
const testFilenames = ref([
  '毕业论文1.docx',
  '财务报表.xlsx',
  '演示文稿.pptx',
  '用户手册.pdf',
  '照片.jpg',
  '视频.mp4',
  '音乐.mp3',
  '压缩包.zip',
  '文本文件.txt',
  '未知文件.unknown'
]);

// 文件图标配置
const fileIcons = computed(() => ({
  folder: 'FolderOutline',
  docx: 'DocumentTextOutline',
  doc: 'DocumentTextOutline',
  sheet: 'GridOutline',
  presentation: 'EaselOutline',
  pdf: 'DocumentOutline',
  image: 'ImageOutline',
  video: 'VideocamOutline',
  audio: 'MusicalNotesOutline',
  default: 'DocumentOutline'
}));

// 文件颜色配置
const fileColors = computed(() => ({
  folder: '#3b82f6',
  doc: '#2563eb',
  docx: '#2563eb',
  sheet: '#059669',
  presentation: '#dc2626',
  pdf: '#dc2626',
  image: '#7c3aed',
  video: '#ea580c',
  audio: '#0891b2',
  default: '#6b7280'
}));

// 下拉菜单选项
const dropdownOptions = ref([
  { label: '打开', key: 'open' },
  { label: '下载', key: 'download' },
  { label: '分享', key: 'share' },
  { type: 'divider' },
  { label: '重命名', key: 'rename' },
  { label: '删除', key: 'delete' }
]);

// 事件处理函数
const handleFileClick = (event, file) => {
  console.log('文件点击:', file);
};

const handleToggleStarred = (file) => {
  console.log('切换标星:', file);
  testFile.value.starred = !testFile.value.starred;
};

const handleDropdownSelect = (key, file) => {
  console.log('下拉菜单选择:', key, file);
};
</script>

<style scoped>
.file-test-container {
  max-width: 1200px;
  margin: 0 auto;
}
</style>
