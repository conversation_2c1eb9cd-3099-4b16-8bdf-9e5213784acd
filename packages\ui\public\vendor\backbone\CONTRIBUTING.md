## How to Open a Backbone.js Ticket

* Do not use tickets to ask for help with (debugging) your application. Ask on
the [mailing list](https://groups.google.com/forum/#!forum/backbonejs),
in the IRC channel (`#documentcloud` on Freenode), or if you understand your
specific problem, on [StackOverflow](http://stackoverflow.com/questions/tagged/backbone.js).

* Before you open a ticket or send a pull request,
[search](https://github.com/jashkenas/backbone/issues) for previous
discussions about the same feature or issue. Add to the earlier ticket if you
find one.

* Before sending a pull request for a feature or bug fix, be sure to have
[tests](http://backbonejs.org/test/) and to document any new functionality in
the `index.html`.

* Use the same coding style as the rest of the
[codebase](https://github.com/jashkenas/backbone/blob/master/backbone.js).

* In your pull request, do not regenerate the annotated sources or rebuild the
minified `backbone-min.js` file. We'll do that before cutting a new release.

* All pull requests should be made to the `master` branch.
