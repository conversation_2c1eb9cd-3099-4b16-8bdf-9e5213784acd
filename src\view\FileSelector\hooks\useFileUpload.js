import { useMessage } from 'naive-ui';
import * as fileApi from '@/api/file.js';

/**
 * 文件上传 Hook
 * 处理文件上传相关功能
 */
export function useFileUpload() {
  const message = useMessage();

  // 处理文件上传
  const handleFileUpload = async (files, { currentPath, loadFileList, loadSidebarStats }) => {
    if (!files || files.length === 0) return;

    try {
      for (const file of files) {
        const uploadParams = {
          folder_id: currentPath.value.at(-1).id,
          file: file,
          file_name: file.name
        };

        const response = await fileApi.uploadFile(uploadParams);
        
        if (response.isSuccess) {
          message.success(`文件 ${file.name} 上传成功`);
        } else {
          message.error(`文件 ${file.name} 上传失败: ${response.message}`);
        }
      }

      await loadFileList();
      await loadSidebarStats();
    } catch (error) {
      console.error('文件上传出错:', error);
      message.error('文件上传失败，请稍后重试');
    }
  };

  // 处理拖拽上传
  const handleDrop = async (event, { currentPath, loadFileList, loadSidebarStats }) => {
    event.preventDefault();
    const files = Array.from(event.dataTransfer.files);
    await handleFileUpload(files, { currentPath, loadFileList, loadSidebarStats });
  };

  // 处理拖拽悬停
  const handleDragOver = (event) => {
    event.preventDefault();
  };

  // 处理拖拽进入
  const handleDragEnter = (event) => {
    event.preventDefault();
  };

  // 处理拖拽离开
  const handleDragLeave = (event) => {
    event.preventDefault();
  };

  return {
    handleFileUpload,
    handleDrop,
    handleDragOver,
    handleDragEnter,
    handleDragLeave
  };
}