<template>
  <div
    class="file-card file-card-regular relative rounded-lg overflow-hidden transition-all duration-200"
    :class="{ 'selected': isSelected }"
    :data-type="file.type"
    :style="{
      backgroundColor: themeStore.isDark ? '#2a2a2a' : '#fff',
      borderColor: isSelected ? themeColors.primary : themeColors.borderColor,
      boxShadow: isSelected ? `0 0 0 2px ${themeColors.primary}` : 'none'
    }"
    @click="handleClick($event)"
    @contextmenu="handleContextMenu($event)"
  >
    <!-- 多选框 -->
    <div
      v-if="isMultiSelectMode"
      class="absolute top-2 left-2 z-10"
      @click.stop="handleCheckboxClick"
    >
      <n-checkbox :checked="isSelected" />
    </div>

    <!-- 标星按钮 -->
    <div
      class="absolute top-2 right-2 z-10 star-button"
      @click.stop="$emit('toggle-starred', file)"
    >
      <n-icon size="18" :color="file.starred ? '#f59e0b' : themeColors.iconColor">
        <component :is="file.starred ? StarSharp : StarOutline" />
      </n-icon>
    </div>

    <!-- 文件图标 -->
    <div class="file-icon-container flex items-center justify-center p-4">
      <div
        class="file-icon-wrapper file-icon-wrapper-regular rounded-lg p-3 transition-all duration-200"
        :style="getIconWrapperStyle()"
      >
        <n-icon
          size="40"
          :color="getFileColor()"
          class="transition-transform duration-200"
        >
          <component :is="getFileIcon()" />
        </n-icon>
      </div>
    </div>

    <!-- 文件信息 -->
    <div class="file-info p-3 pt-0 text-center">
      <div
        class="file-name font-medium truncate mb-1 transition-colors duration-200"
        :style="{ color: themeColors.textColor }"
      >
        {{ file.name }}
      </div>
      <div class="file-meta flex justify-center items-center text-xs" :style="{ color: themeColors.textSecondary }">
        <span>{{ formatFileSize(file.size) }}</span>
        <span class="mx-1" v-if="file.size">•</span>
        <span>{{ formatDate(file.modifiedAt) }}</span>
      </div>
    </div>

    <!-- 操作下拉菜单 -->
    <div class="absolute bottom-2 right-2 menu-button" @click.stop>
      <n-dropdown
        trigger="click"
        :options="processedDropdownOptions"
        @select="key => $emit('dropdown-select', key, file)"
        @click.stop
      >
        <div>
          <n-button quaternary circle size="small">
            <template #icon>
              <n-icon>
                <EllipsisHorizontal />
              </n-icon>
            </template>
          </n-button>
        </div>
      </n-dropdown>
    </div>
  </div>
</template>

<script setup>
import { computed, h } from 'vue';
import { NIcon, NCheckbox, NButton, NDropdown } from 'naive-ui';
import { useThemeStore } from '../../stores/theme';
import {
  StarOutline,
  StarSharp,
  EllipsisHorizontal,
  DocumentTextOutline,
  GridOutline,
  EaselOutline,
  DocumentOutline,
  ImageOutline,
  VideocamOutline,
  MusicalNotesOutline,
  ArchiveOutline,
  CodeSlashOutline,
  TerminalOutline,
  DocumentAttachOutline,
  ReaderOutline,
  CameraOutline,
  ColorPaletteOutline,
  FolderOutline
} from '@vicons/ionicons5';

const props = defineProps({
  file: {
    type: Object,
    required: true
  },
  fileIcons: {
    type: Object,
    required: true
  },
  fileColors: {
    type: Object,
    required: true
  },
  dropdownOptions: {
    type: Array,
    default: () => []
  },
  isMultiSelectMode: {
    type: Boolean,
    default: false
  },
  isSelected: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['click', 'toggle-starred', 'dropdown-select', 'checkbox-click', 'context-menu']);

const themeStore = useThemeStore();
const themeColors = computed(() => themeStore.getThemeColors());

// 图标组件映射
const iconComponents = {
  FolderOutline,
  DocumentTextOutline,
  GridOutline,
  EaselOutline,
  DocumentOutline,
  ImageOutline,
  VideocamOutline,
  MusicalNotesOutline
};

// 文件类型图标映射
const fileIconMap = {
  doc: DocumentTextOutline,
  docx: DocumentTextOutline,
  sheet: GridOutline,
  xlsx: GridOutline,
  xls: GridOutline,
  presentation: EaselOutline,
  pptx: EaselOutline,
  ppt: EaselOutline,
  pdf: ReaderOutline,
  txt: DocumentAttachOutline,
  text: DocumentAttachOutline,
  image: ImageOutline,
  jpg: CameraOutline,
  jpeg: CameraOutline,
  png: ImageOutline,
  gif: ColorPaletteOutline,
  svg: ColorPaletteOutline,
  video: VideocamOutline,
  mp4: VideocamOutline,
  avi: VideocamOutline,
  mov: VideocamOutline,
  audio: MusicalNotesOutline,
  mp3: MusicalNotesOutline,
  wav: MusicalNotesOutline,
  archive: ArchiveOutline,
  zip: ArchiveOutline,
  rar: ArchiveOutline,
  '7z': ArchiveOutline,
  tar: ArchiveOutline,
  gz: ArchiveOutline,
  code: CodeSlashOutline,
  js: CodeSlashOutline,
  ts: CodeSlashOutline,
  html: CodeSlashOutline,
  css: CodeSlashOutline,
  vue: CodeSlashOutline,
  py: CodeSlashOutline,
  java: CodeSlashOutline,
  cpp: CodeSlashOutline,
  c: CodeSlashOutline,
  exe: TerminalOutline,
  app: TerminalOutline,
  default: DocumentOutline
};

// 处理下拉菜单选项，为图标添加正确的渲染函数
const processedDropdownOptions = computed(() => {
  return props.dropdownOptions.map(option => {
    if (option.type === 'divider') {
      return option;
    }

    return {
      ...option,
      icon: option.icon ? () => h(NIcon, null, { default: () => h(option.icon) }) : undefined
    };
  });
});

// 获取文件图标
const getFileIcon = () => {
  // 优先使用传递的 fileIcons props，如果没有则使用内置映射
  const iconName = props.fileIcons[props.file.type] || props.fileIcons.default;
  if (iconName && iconComponents[iconName]) {
    return iconComponents[iconName];
  }
  return fileIconMap[props.file.type] || fileIconMap.default;
};

// 获取文件颜色
const getFileColor = () => {
  return props.fileColors[props.file.type] || props.fileColors.default;
};

// 获取图标包装器样式
const getIconWrapperStyle = () => {
  return {
    backgroundColor: themeStore.isDark ? '#3a3a3a' : '#f5f5f5'
  };
};

// 处理点击事件
const handleClick = (event) => {
  emit('click', event, props.file);
};

// 处理复选框点击
const handleCheckboxClick = () => {
  emit('checkbox-click', props.file);
};

// 处理右键菜单
const handleContextMenu = (event) => {
  event.preventDefault();
  emit('context-menu', event, props.file);
};

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (!bytes || bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-';

  const date = new Date(dateString);
  const now = new Date();
  const diffTime = Math.abs(now - date);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 1) return '今天';
  if (diffDays === 2) return '昨天';
  if (diffDays <= 7) return `${diffDays} 天前`;

  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};
</script>

<style scoped>
.file-card {
  border: 1px solid;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  padding-left: 10px;
  padding-right: 10px;
}

/* 普通文件卡片样式 */
.file-card-regular:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px v-bind('themeStore.isDark ? "rgba(0, 0, 0, 0.3)" : "rgba(0, 0, 0, 0.1)"');
}

/* 普通文件图标包装器 */
.file-icon-wrapper-regular:hover {
  transform: scale(1.05);
  background-color: v-bind('themeStore.isDark ? "#4a4a4a" : "#e5e7eb"');
}

.star-button,
.menu-button {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.file-card:hover .star-button,
.file-card:hover .menu-button,
.file-card.selected .star-button,
.file-card.selected .menu-button {
  opacity: 1;
}

.file-card.selected {
  border-width: 2px;
}

.file-icon-container {
  padding-top: 1.5rem;
}

/* 文件类型特殊效果 */
.file-card[data-type="image"]:hover .file-icon-wrapper-regular {
  background: linear-gradient(135deg, #7c3aed20, #7c3aed10);
  border: 1px solid #7c3aed30;
}

.file-card[data-type="video"]:hover .file-icon-wrapper-regular {
  background: linear-gradient(135deg, #ea580c20, #ea580c10);
  border: 1px solid #ea580c30;
}

.file-card[data-type="audio"]:hover .file-icon-wrapper-regular {
  background: linear-gradient(135deg, #0891b220, #0891b210);
  border: 1px solid #0891b230;
}

.file-card[data-type="doc"]:hover .file-icon-wrapper-regular,
.file-card[data-type="docx"]:hover .file-icon-wrapper-regular {
  background: linear-gradient(135deg, #2563eb20, #2563eb10);
  border: 1px solid #2563eb30;
}

.file-card[data-type="pdf"]:hover .file-icon-wrapper-regular {
  background: linear-gradient(135deg, #dc262620, #dc262610);
  border: 1px solid #dc262630;
}
</style>