import { useMessage } from 'naive-ui';
import { useRouter } from 'vue-router';
import * as fileApi from '@/api/file.js';

/**
 * 文件交互 Hook
 * 处理文件和文件夹的点击交互
 */
export function useFileInteraction() {
    const message = useMessage();
    const router = useRouter();

    // 处理文件点击
    const handleFileClick = (file, {
        selectedFile,
        showFileDetails,
        isMultiSelectMode,
        selectedFiles,
        fileSelection
    }) => {
        if (isMultiSelectMode.value) {
            fileSelection.handleMultiSelect(file, { selectedFiles });
        } else {
            selectedFile.value = file;
            showFileDetails.value = true;
        }
    };

    // 处理文件双击
    const handleFileDoubleClick = async (file, { selectedFile, showFileDetails }) => {
        const editableTypes = ['doc', 'sheet', 'presentation', 'pdf', 'text', 'md'];
        const mediaTypes = ['image', 'video', 'audio'];

        if (editableTypes.includes(file.type)) {
            // 跳转到编辑器
            await router.push({
                path: '/editor',
                query: {
                    fileId: file.id,
                    fileName: file.name,
                    fileType: file.type
                }
            });
        } else if (mediaTypes.includes(file.type)) {
            // 显示预览
            selectedFile.value = file;
            showFileDetails.value = true;
        } else {
            // 直接下载
            try {
                const response = await fileApi.downloadFile(file.id);
                if (response.isSuccess) {
                    const url = window.URL.createObjectURL(response.blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = file.name;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    window.URL.revokeObjectURL(url);
                    message.success(`文件 ${file.name} 下载成功`);
                } else {
                    message.error(`下载失败: ${response.message}`);
                }
            } catch (error) {
                console.error('下载文件出错:', error);
                message.error('下载失败，请稍后重试');
            }
        }
    };

    // 处理文件夹点击
    const handleFolderClick = async (folder, {
        currentPath,
        pagination,
        loadFileList,
        loading,
        isMultiSelectMode,
        selectedFiles,
        fileSelection
    }) => {
        // 如果是多选模式，则进行选择操作
        if (isMultiSelectMode.value) {
            fileSelection.handleMultiSelect(folder, { selectedFiles });
            return;
        }

        try {
            loading.value = true;

            currentPath.value.push({
                name:folder.name,
                id: folder.id
            });
            await loadFileList({
                sort: 'modified_time',
                order: 'desc',
                folder_id: folder.id,
            });
        } catch (error) {
            console.error('获取文件夹详情失败:', error);
            message.error('无法访问文件夹，请稍后重试');
        } finally {
            loading.value = false;
        }
    };

    return {
        handleFileClick,
        handleFileDoubleClick,
        handleFolderClick
    };
}