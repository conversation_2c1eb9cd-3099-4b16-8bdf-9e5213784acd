html,
body {
	margin: 0;
	padding: 0;
}

body {
	font: 14px "Helvetica Neue", Helvetica, Arial, sans-serif;
	line-height: 1.4em;
	background: #eeeeee;
	color: #333333;
	width: 520px;
	margin: 0 auto;
	-webkit-font-smoothing: antialiased;
}

#todoapp {
	background: #fff;
	padding: 20px;
	margin-bottom: 40px;
	-webkit-box-shadow: rgba(0, 0, 0, 0.2) 0 2px 6px 0;
	-moz-box-shadow: rgba(0, 0, 0, 0.2) 0 2px 6px 0;
	-ms-box-shadow: rgba(0, 0, 0, 0.2) 0 2px 6px 0;
	-o-box-shadow: rgba(0, 0, 0, 0.2) 0 2px 6px 0;
	box-shadow: rgba(0, 0, 0, 0.2) 0 2px 6px 0;
	-webkit-border-radius: 0 0 5px 5px;
	-moz-border-radius: 0 0 5px 5px;
	-ms-border-radius: 0 0 5px 5px;
	-o-border-radius: 0 0 5px 5px;
	border-radius: 0 0 5px 5px;
}

#todoapp h1 {
	font-size: 36px;
	font-weight: bold;
	text-align: center;
	padding: 0 0 10px 0;
}

#todoapp input[type="text"] {
	width: 466px;
	font-size: 24px;
	font-family: inherit;
	line-height: 1.4em;
	border: 0;
	outline: none;
	padding: 6px;
	border: 1px solid #999999;
	-webkit-box-shadow: rgba(0, 0, 0, 0.2) 0 1px 2px 0 inset;
	-moz-box-shadow: rgba(0, 0, 0, 0.2) 0 1px 2px 0 inset;
	-ms-box-shadow: rgba(0, 0, 0, 0.2) 0 1px 2px 0 inset;
	-o-box-shadow: rgba(0, 0, 0, 0.2) 0 1px 2px 0 inset;
	box-shadow: rgba(0, 0, 0, 0.2) 0 1px 2px 0 inset;
}

#todoapp input::-webkit-input-placeholder {
	font-style: italic;
}

#main {
	display: none;
}

#todo-list {
	margin: 10px 0;
	padding: 0;
	list-style: none;
}

#todo-list li {
	padding: 18px 20px 18px 0;
	position: relative;
	font-size: 24px;
	border-bottom: 1px solid #cccccc;
}

#todo-list li:last-child {
	border-bottom: none;
}

#todo-list li.done label {
	color: #777777;
	text-decoration: line-through;
}

#todo-list .destroy {
	position: absolute;
	right: 5px;
	top: 20px;
	display: none;
	cursor: pointer;
	width: 20px;
	height: 20px;
	background: url(destroy.png) no-repeat;
}

#todo-list li:hover .destroy {
  	display: block;
}

#todo-list .destroy:hover {
  	background-position: 0 -20px;
}

#todo-list li.editing {
	border-bottom: none;
	margin-top: -1px;
	padding: 0;
}

#todo-list li.editing:last-child {
	margin-bottom: -1px;
}

#todo-list li.editing .edit {
	display: block;
	width: 444px;
	padding: 13px 15px 14px 20px;
	margin: 0;
}

#todo-list li.editing .view {
	display: none;
}

#todo-list li .view label {
	word-break: break-word;
}

#todo-list li .edit {
	display: none;
}

#todoapp footer {
	display: none;
	margin: 0 -20px -20px -20px;
	overflow: hidden;
	color: #555555;
	background: #f4fce8;
	border-top: 1px solid #ededed;
	padding: 0 20px;
	line-height: 37px;
	-webkit-border-radius: 0 0 5px 5px;
	-moz-border-radius: 0 0 5px 5px;
	-ms-border-radius: 0 0 5px 5px;
	-o-border-radius: 0 0 5px 5px;
	border-radius: 0 0 5px 5px;
}

#clear-completed {
	float: right;
	line-height: 20px;
	text-decoration: none;
	background: rgba(0, 0, 0, 0.1);
	color: #555555;
	font-size: 11px;
	margin-top: 8px;
	margin-bottom: 8px;
	padding: 0 10px 1px;
	cursor: pointer;
	-webkit-border-radius: 12px;
	-moz-border-radius: 12px;
	-ms-border-radius: 12px;
	-o-border-radius: 12px;
	border-radius: 12px;
	-webkit-box-shadow: rgba(0, 0, 0, 0.2) 0 -1px 0 0;
	-moz-box-shadow: rgba(0, 0, 0, 0.2) 0 -1px 0 0;
	-ms-box-shadow: rgba(0, 0, 0, 0.2) 0 -1px 0 0;
	-o-box-shadow: rgba(0, 0, 0, 0.2) 0 -1px 0 0;
	box-shadow: rgba(0, 0, 0, 0.2) 0 -1px 0 0;
}

#clear-completed:hover {
	background: rgba(0, 0, 0, 0.15);
	-webkit-box-shadow: rgba(0, 0, 0, 0.3) 0 -1px 0 0;
	-moz-box-shadow: rgba(0, 0, 0, 0.3) 0 -1px 0 0;
	-ms-box-shadow: rgba(0, 0, 0, 0.3) 0 -1px 0 0;
	-o-box-shadow: rgba(0, 0, 0, 0.3) 0 -1px 0 0;
	box-shadow: rgba(0, 0, 0, 0.3) 0 -1px 0 0;
}

#clear-completed:active {
	position: relative;
	top: 1px;
}

#todo-count span {
	font-weight: bold;
}

#instructions {
	margin: 10px auto;
	color: #777777;
	text-shadow: rgba(255, 255, 255, 0.8) 0 1px 0;
	text-align: center;
}

#instructions a {
	color: #336699;
}

#credits {
	margin: 30px auto;
	color: #999;
	text-shadow: rgba(255, 255, 255, 0.8) 0 1px 0;
	text-align: center;
}

#credits a {
	color: #888;
}
