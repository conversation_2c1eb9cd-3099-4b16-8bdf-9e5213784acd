<template>
    <div class="file-selector" 
         @keydown="handleKeydown" 
         @dragenter="fileDragDrop.handleDragEnter"
         @dragleave="fileDragDrop.handleDragLeave"
         @dragover="fileDragDrop.handleDragOver"
         @drop="(e) => fileDragDrop.handleDrop(e, { fileUpload, currentPath, loadFileList, loadSidebarStats })"
         tabindex="0"
         :class="{ 'drag-over': fileDragDrop.isDragOver.value}">
         
        <!-- 调试信息已关闭 -->
         
        <!-- 侧边栏 -->
        <SideBar :user-stats="userStats" :storage-info="storageInfo" :tags="tags" :loading="sidebarLoading"
            @menu-click="handleSidebarMenuClick" @tag-create="handleTagCreate" @tag-update="handleTagUpdate"
            @tag-delete="handleTagDelete" />

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 工具栏 -->
            <ToolBar 
                :search-query="fileSearch.searchQuery.value"
                :view-mode="viewMode"
                :sort-order="sortOrder"
                :is-multi-select-mode="isMultiSelectMode"
                :selected-files-count="selectedFiles.length"
                @update:search-query="handleSearch"
                @update:view-mode="handleViewModeChange"
                @update:sort-order="handleSortToggle"
                @toggle-multi-select="() => fileSelection.toggleMultiSelectMode({ isMultiSelectMode, selectedFiles })"
                @batch-operation="handleBatchOperation"
                @create-folder="showCreateModal = true"
                @create-document="handleCreateDocument"
                @upload="triggerUpload" />

            <!-- 面包屑导航 -->
            <FileBreadcrumb v-show="currentPath" :current-path="currentPath"
                 @navigate="(index) => fileNavigation.navigateToPath(index, { currentPath, loadFileList })" />

            <!-- 文件过滤标签 -->
            <FileTabFilter v-model:value="activeFilter" />

            <!-- 文件内容区域 -->
            <FileContentArea :loading="loading" :filtered-files="filteredFiles" :view-mode="viewMode"
                :selected-files="selectedFiles" :is-multi-select-mode="isMultiSelectMode" :file-icons="fileIcons"
                :file-colors="fileColors" :dropdown-options="dropdownOptions" :search-query="fileSearch.searchQuery.value"
                :is-searching="fileSearch.isSearching.value" @select-file="handleFileSelect"
                @double-click-file="handleFileDoubleClick" @context-menu="handleContextMenu"
                @toggle-starred="handleToggleStarred" @dropdown-select="handleDropdownSelect" 
                @select-multiple="handleSelectMultiple" @create-folder="showCreateModal = true" 
                @create-document="handleCreateDocument" @upload="triggerUpload"
                @clear-search="handleClearSearch" @file-drag-start="(e, file) => fileDragDrop.handleFileDragStart(e, file)"
                @folder-drag-start="(e, folder) => fileDragDrop.handleFolderDragStart(e, folder)"
                @drop-on-folder="(e, folder) => fileDragDrop.handleDropOnFolder(e, folder, { fileOperations, loadFileList, loadSidebarStats })" />

            <!-- 状态栏 -->
            <FileStatusBar 
                :filtered-files-count="filteredFiles.length"
                :selected-files-count="selectedFiles.length"
                :current-path="currentPath"
                :storage-used="storageInfo.used || 0"
                :storage-used-unit="storageInfo.usedUnit || 'B'"
                :storage-total="storageInfo.total || 100"
                :storage-total-unit="storageInfo.totalUnit || 'GB'"
                :storage-usage-percent="storageInfo.usagePercent || 0"
                @toggle-upload="triggerUpload"
                @show-help="showHelpModal = true" />
        </div>

        <!-- 文件详情侧边栏 -->
        <FileDetails v-if="showFileDetails && selectedFile" :file="selectedFile" :show="showFileDetails"
            @close="showFileDetails = false"
            @download="() => fileOperations.handleDownloadFile(selectedFile, { loadFileList, loadSidebarStats })"
            @share="() => fileOperations.handleShareFile(selectedFile, { loadFileList, loadSidebarStats })"
            @rename="() => fileOperations.handleRenameFile(selectedFile, { loadFileList, loadSidebarStats })"
            @copy="() => fileOperations.handleCopyFile(selectedFile, { loadFileList, loadSidebarStats })"
            @move="() => fileOperations.handleMoveFile(selectedFile, { loadFileList, loadSidebarStats })"
            @delete="() => fileOperations.handleDeleteFile(selectedFile, { loadFileList, loadSidebarStats })" />

        <!-- 创建文件/文件夹模态框 -->
        <CreateFileModal :show="showCreateModal" @close="showCreateModal = false"
            @confirm="(data) => fileOperations.handleCreateConfirm(data, { currentPath, loadFileList, loadSidebarStats })" />

        <!-- 帮助模态框 -->
        <HelpModal 
            :show="showHelpModal"
            :shortcuts="keyboardShortcuts.shortcuts"
            @close="showHelpModal = false"
        />

        <!-- 右键菜单 -->
        <ContextMenu
            v-if="showContextMenu"
            :x="contextMenuPosition.x"
            :y="contextMenuPosition.y"
            :options="dropdownOptions"
            @select="handleContextMenuSelect"
            @close="closeContextMenu"
        />
    </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useMessage } from 'naive-ui';
import * as fileApi from '@/api/file.js';
import { getFileType } from '@/utils/fileUtils.js';

// 导入组件
import SideBar from '@/components/file-selector/SideBar.vue';
import ToolBar from '@/components/file-selector/ToolBar.vue';
import FileBreadcrumb from "@/components/file-selector/FileBreadcrumb.vue";
import FileTabFilter from '@/components/file-selector/FileTabFilter.vue';
import FileContentArea from '@/components/file-selector/FileContentArea.vue';
import FileStatusBar from '@/components/file-selector/FileStatusBar.vue';
import FileDetails from '@/components/file-selector/FileDetails.vue';
import CreateFileModal from '@/components/file-selector/CreateFileModal.vue';
import HelpModal from '@/components/file-selector/HelpModal.vue';
import ContextMenu from '@/components/file-selector/ContextMenu.vue';
import { dropdownOptions } from "./constant.js"
import { useUploadTrigger } from "@/hooks/useUploadTrigger/useUploadTrigger.js";

// 导入 hooks
import {
    useFileOperations,
    useFileSelection,
    useFileNavigation,
    useBatchOperations,
    useFileUpload,
    useFileInteraction,
    useKeyboardShortcuts,
    useFileDragDrop,
    useFileSearch
} from './hooks/index.js';

const message = useMessage();

// ==================== 状态变量声明 ====================
// 加载状态
const loading = ref(false);
const sidebarLoading = ref(false);

// 文件数据
const files = ref([]);
const selectedFiles = ref([]);
const selectedFile = ref(null);
const totalFiles = ref(0);

// 用户和存储信息
const userStats = ref({});
const storageInfo = ref({});
const tags = ref([]);

// 导航和路径
const currentPath = ref([{
    name: '全部文件',
    id: 0
}]);


// 过滤和排序
const activeFilter = ref('all');
const sortBy = ref('name');
const sortOrder = ref('asc');
const viewMode = ref('grid');

// UI 状态
const isMultiSelectMode = ref(false);
const showFileDetails = ref(false);
const showCreateModal = ref(false);
const showHelpModal = ref(false);
const showContextMenu = ref(false);
const contextMenuPosition = ref({ x: 0, y: 0 });
const contextMenuFile = ref(null);


// ==================== 计算属性 ====================
// 文件图标配置
const fileIcons = computed(() => ({
    folder: 'FolderOutline',
    docx: 'DocumentTextOutline',
    doc: 'DocumentTextOutline',
    sheet: 'GridOutline',
    presentation: 'EaselOutline',
    pdf: 'DocumentOutline',
    image: 'ImageOutline',
    video: 'VideocamOutline',
    audio: 'MusicalNotesOutline',
    default: 'DocumentOutline'
}));

// 文件颜色配置
const fileColors = computed(() => ({
    folder: '#3b82f6',
    doc: '#2563eb',
    sheet: '#059669',
    presentation: '#dc2626',
    pdf: '#dc2626',
    image: '#7c3aed',
    video: '#ea580c',
    audio: '#0891b2',
    default: '#6b7280'
}));

// 过滤后的文件列表
const filteredFiles = computed(() => {
    // 如果有搜索结果，优先显示搜索结果
    let result = fileSearch.searchResults.value.length > 0 ? fileSearch.searchResults.value : files.value;

    // 按类型过滤
    if (activeFilter.value !== 'all') {
        result = result.filter(file => {
            switch (activeFilter.value) {
                case 'docs': return ['doc', 'sheet', 'presentation', 'pdf', 'txt'].includes(file.type);
                case 'images': return file.type === 'image';
                case 'videos': return file.type === 'video';
                case 'audio': return file.type === 'audio';
                case 'archives': return ['zip', 'rar', '7z', 'tar', 'gz'].includes(file.type);
                case 'others': return !['doc', 'sheet', 'presentation', 'pdf', 'txt', 'image', 'video', 'audio', 'zip', 'rar', '7z', 'tar', 'gz', 'folder'].includes(file.type);
                default: return true;
            }
        });
    }

    // 排序 - 文件夹优先显示
    result.sort((a, b) => {
        // 文件夹优先
        if (a.type === 'folder' && b.type !== 'folder') return -1;
        if (a.type !== 'folder' && b.type === 'folder') return 1;

        let aValue = a[sortBy.value];
        let bValue = b[sortBy.value];

        if (sortBy.value === 'size') {
            aValue = a.size || 0;
            bValue = b.size || 0;
        } else if (sortBy.value === 'modifiedAt') {
            aValue = new Date(a.modifiedAt);
            bValue = new Date(b.modifiedAt);
        }

        if (sortOrder.value === 'asc') {
            return aValue > bValue ? 1 : -1;
        } else {
            return aValue < bValue ? 1 : -1;
        }
    });

    return result;
});

// 文件类型统计
const fileCounts = computed(() => {
    const counts = {
        all: files.value.length,
        folder: 0,
        document: 0,
        image: 0,
        video: 0,
        audio: 0,
        other: 0
    };

    files.value.forEach(file => {
        switch (file.type) {
            case 'folder': counts.folder++; break;
            case 'doc':
            case 'sheet':
            case 'presentation':
            case 'pdf': counts.document++; break;
            case 'image': counts.image++; break;
            case 'video': counts.video++; break;
            case 'audio': counts.audio++; break;
            default: counts.other++; break;
        }
    });

    return counts;
});

// ==================== 使用 Hooks ====================
const fileOperations = useFileOperations();
const fileSelection = useFileSelection();
const fileNavigation = useFileNavigation();
const batchOperations = useBatchOperations();
const fileUpload = useFileUpload();
const fileInteraction = useFileInteraction();
const keyboardShortcuts = useKeyboardShortcuts();
const fileDragDrop = useFileDragDrop();
const fileSearch = useFileSearch();

// 在组件中使用
const { triggerUpload, cleanup } = useUploadTrigger({
  multiple: true,
  accept: '.pdf,.doc,.docx',
  onFileSelect: (files) => {
    // 处理选中的文件
    fileUpload.handleFileUpload(files, { currentPath, loadFileList, loadSidebarStats });
  }
});

// ==================== 数据加载方法 ====================
// 加载文件列表
const loadFileList = async (params = {}) => {
    try {
        loading.value = true;
        const requestParams = {
            sort: sortBy.value,
            order: sortOrder.value,
            ...params
        };
        
        const response = await fileApi.getFileList(requestParams);

        if (response.isSuccess) {
            // 合并文件和文件夹数据
            const fileList = (response.data.files || []).map(file => ({
                id: file.file_id,
                name: file.file_name,
                type: file.file_type || getFileType(file.file_name),
                size: file.file_size,
                modifiedAt: file.update_time,
                createdAt: file.upload_time,
                starred: file.is_starred,
                tags: file.tags,
                status: file.status
            }));
            const folderList = (response.data.folders || []).map(folder => ({
                id: folder.folder_id,
                name: folder.folder_name,
                type: 'folder',
                size: 0,
                modifiedAt: folder.update_time,
                createdAt: folder.create_time,
                parentId: folder.parent_id,
                status: folder.status
            }));

            files.value = [...folderList, ...fileList];
            totalFiles.value = response.data.total || (response.data.file_count + response.data.folder_count) || 0;
        } else {
            message.error(`加载文件列表失败: ${response.message}`);
        }
    } catch (error) {
        console.error('加载文件列表出错:', error);
        message.error('加载文件列表失败，请稍后重试');
    } finally {
        loading.value = false;
    }
};

// 字节转换为友好的单位
const formatBytes = (bytes, decimals = 2) => {
    if (bytes === 0) return { value: 0, unit: 'B' };
    
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
    
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return {
        value: parseFloat((bytes / Math.pow(k, i)).toFixed(dm)),
        unit: sizes[i]
    };
};

// 加载侧边栏统计信息
const loadSidebarStats = async () => {
    try {
        sidebarLoading.value = true;

        // 并行加载用户统计和标签
        const [userStatsRes, tagsRes] = await Promise.all([
            fileApi.getUserStats(),
            fileApi.getTags()
        ]);

        if (userStatsRes.isSuccess) {
            userStats.value = userStatsRes.data;
            
            // 获取原始字节数据
            const usedBytes = parseFloat(userStatsRes.data.used_storage || 0);
            const totalBytes = parseFloat(userStatsRes.data.total_storage || 107374182400); // 默认100GB
            
            // 转换为友好的单位
            const usedFormatted = formatBytes(usedBytes);
            const totalFormatted = formatBytes(totalBytes);
            
            // 计算使用百分比
            const usagePercent = totalBytes > 0 ? (usedBytes / totalBytes) * 100 : 0;
            
            storageInfo.value = {
                used: usedFormatted.value,
                usedUnit: usedFormatted.unit,
                total: totalFormatted.value,
                totalUnit: totalFormatted.unit,
                usedBytes: usedBytes,
                totalBytes: totalBytes,
                usagePercent: parseFloat(usagePercent.toFixed(2))
            };
        }

        if (tagsRes.isSuccess) {
            tags.value = tagsRes.data || [];
        }
    } catch (error) {
        console.error('加载侧边栏数据出错:', error);
        // 设置默认存储信息
        storageInfo.value = {
            used: 0,
            usedUnit: 'B',
            total: 100,
            totalUnit: 'GB',
            usedBytes: 0,
            totalBytes: 107374182400,
            usagePercent: 0
        };
    } finally {
        sidebarLoading.value = false;
    }
};

// ==================== 事件处理方法 ====================
// 侧边栏菜单点击
const handleSidebarMenuClick = (action) => {
    switch (action) {
        case 'recent':
            // 显示最近文件
            break;
        case 'shared':
            // 显示共享文件
            break;
        case 'trash':
            // 显示回收站
            break;
        case 'help':
            showHelpModal.value = true;
            break;
    }
};

// 标签管理
const handleTagCreate = async (tagData) => {
    try {
        const response = await fileApi.createTag(tagData);
        if (response.isSuccess) {
            message.success('标签创建成功');
            await loadSidebarStats();
        } else {
            message.error(`创建失败: ${response.message}`);
        }
    } catch (error) {
        console.error('创建标签出错:', error);
        message.error('创建失败，请稍后重试');
    }
};

const handleTagUpdate = async (tagId, tagData) => {
    try {
        const response = await fileApi.updateTag(tagId, tagData);
        if (response.isSuccess) {
            message.success('标签更新成功');
            await loadSidebarStats();
        } else {
            message.error(`更新失败: ${response.message}`);
        }
    } catch (error) {
        console.error('更新标签出错:', error);
        message.error('更新失败，请稍后重试');
    }
};

const handleTagDelete = async (tagId) => {
    try {
        const response = await fileApi.deleteTag(tagId);
        if (response.isSuccess) {
            message.success('标签删除成功');
            await loadSidebarStats();
        } else {
            message.error(`删除失败: ${response.message}`);
        }
    } catch (error) {
        console.error('删除标签出错:', error);
        message.error('删除失败，请稍后重试');
    }
};

// 文件选择处理
const handleFileSelect = (file) => {
    if (file.type === 'folder') {
        // 处理文件夹点击
        fileInteraction.handleFolderClick(file, {
            currentPath,
            loadFileList,
            loading,
            isMultiSelectMode,
            selectedFiles,
            fileSelection
        });
    } else {
        // 处理文件点击
        fileInteraction.handleFileClick(file, {
            selectedFile,
            showFileDetails,
            isMultiSelectMode,
            selectedFiles,
            fileSelection
        });
    }
};

// 文件双击处理
const handleFileDoubleClick = (file) => {
    fileInteraction.handleFileDoubleClick(file, {
        selectedFile,
        showFileDetails
    });
};

// 切换收藏状态
const handleToggleStarred = async (file) => {
    try {
        const response = await fileApi.toggleFileStarred(file.id, !file.starred);
        if (response.isSuccess) {
            // 更新本地状态
            const index = files.value.findIndex(f => f.id === file.id);
            if (index !== -1) {
                files.value[index].starred = !file.starred;
            }
            message.success(file.starred ? '已取消收藏' : '已添加收藏');
        } else {
            message.error(`操作失败: ${response.message}`);
        }
    } catch (error) {
        console.error('切换收藏状态出错:', error);
        message.error('操作失败，请稍后重试');
    }
};

// 下拉菜单选择处理
const handleDropdownSelect = async (action, file) => {
    switch (action) {
        case 'download':
            await fileOperations.handleDownloadFile(file, { loadFileList, loadSidebarStats });
            break;
        case 'move':
            await fileOperations.handleMoveFile(file, { loadFileList, loadSidebarStats });
            break;
        case 'copy':
            await fileOperations.handleCopyFile(file, { loadFileList, loadSidebarStats });
            break;
        case 'rename':
            await fileOperations.handleRenameFile(file, { loadFileList, loadSidebarStats });
            break;
        case 'delete':
            await fileOperations.handleDeleteFile(file, { loadFileList, loadSidebarStats });
            break;
        default:
            console.warn('未知的操作:', action);
    }
};

// 多选处理
const handleSelectMultiple = (file, checked) => {
    if (checked) {
        if (!selectedFiles.value.find(f => f.id === file.id)) {
            selectedFiles.value.push(file);
        }
} else {
        selectedFiles.value = selectedFiles.value.filter(f => f.id !== file.id);
    }
};

// 创建文档处理
const handleCreateDocument = async (type) => {
    try {
        const response = await fileApi.createDocument({
            name: `新建${getDocumentTypeName(type)}`,
            file_type: type,
            parent_id: currentPath.value[currentPath.value.length - 1].id
        });

        if (response.isSuccess) {
            message.success('文档创建成功');
            await loadFileList();
            await loadSidebarStats();
        } else {
            message.error(`创建失败: ${response.message}`);
        }
    } catch (error) {
        console.error('创建文档出错:', error);
        message.error('创建失败，请稍后重试');
    }
};

// 获取文档类型名称
const getDocumentTypeName = (type) => {
    const typeNames = {
        doc: 'Word文档',
        sheet: 'Excel表格',
        presentation: 'PPT演示文稿',
        text: '文本文档'
    };
    return typeNames[type] || '文档';
};

// 视图模式变更
const handleViewModeChange = (mode) => {
    viewMode.value = mode;
};

// 排序切换
const handleSortToggle = () => {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc';
    loadFileList();
};

// 批量操作处理
const handleBatchOperation = async (action) => {
    switch (action) {
        case 'download':
            await batchOperations.handleBatchDownload({ selectedFiles, loadFileList, loadSidebarStats });
            break;
        case 'share':
            // 批量分享功能
            message.info('批量分享功能开发中...');
            break;
        case 'delete':
            await batchOperations.handleBatchDelete({ selectedFiles, loadFileList, loadSidebarStats });
            break;
        default:
            console.warn('未知的批量操作:', action);
    }
};

// 搜索处理
const handleSearch = (query) => {
    if (query.trim()) {
        fileSearch.performSearch(query, { currentPath });
    } else {
        fileSearch.clearSearch();
    }
};

// 清除搜索
const handleClearSearch = () => {
    fileSearch.clearSearch();
};


// 右键菜单处理
const handleContextMenu = (event, file) => {
    event.preventDefault();
    contextMenuFile.value = file;
    contextMenuPosition.value = {
        x: event.clientX,
        y: event.clientY
    };
    showContextMenu.value = true;
};

const handleContextMenuSelect = async (action) => {
    showContextMenu.value = false;
    const file = contextMenuFile.value;
    
    if (!file) return;
    
    await handleDropdownSelect(action, file);
};

const closeContextMenu = () => {
    showContextMenu.value = false;
    contextMenuFile.value = null;
};

// 键盘事件处理
const handleKeydown = (event) => {
    keyboardShortcuts.handleKeydown(event, {
        selectedFiles,
        filteredFiles,
        isMultiSelectMode,
        fileOperations,
        batchOperations,
        fileSelection,
        loadFileList,
        loadSidebarStats,
        showCreateModal,
        showHelpModal,
        selectedFile,
        showFileDetails,
        currentPath,
        loading,
        fileInteraction,
        fileUpload,
        fileSearch
    });
};

// ==================== 生命周期 ====================
onMounted(async () => {
    await Promise.all([
        loadFileList(),
        loadSidebarStats()
    ]);

    // 添加键盘事件监听
    document.addEventListener('keydown', handleKeydown);
    
    // 添加全局拖拽状态重置监听器
    const handleGlobalDragEnd = () => {
        fileDragDrop.resetDragState();
    };
    
    document.addEventListener('dragend', handleGlobalDragEnd);
    document.addEventListener('drop', handleGlobalDragEnd);
    
    // 存储清理函数
    window._fileSelectorCleanup = () => {
        document.removeEventListener('keydown', handleKeydown);
        document.removeEventListener('dragend', handleGlobalDragEnd);
        document.removeEventListener('drop', handleGlobalDragEnd);
    };
});

onUnmounted(() => {
    // 执行清理
    if (window._fileSelectorCleanup) {
        window._fileSelectorCleanup();
        delete window._fileSelectorCleanup;
    }

    cleanup();
});
</script>

<style lang="scss" scoped>
.file-selector {
    display: flex;
    height: 100vh;
    background: var(--bg-color);
    outline: none;
    position: relative;

    &.drag-over {
        &::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(24, 144, 255, 0.1);
            border: 2px dashed #1890ff;
            border-radius: 8px;
            z-index: 1000;
            pointer-events: none;
        }
    }

    .main-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        padding: 20px;
    }
}
</style>
