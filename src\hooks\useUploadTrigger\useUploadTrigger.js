export function useUploadTrigger(options = {}) {
  const fileInputRef = ref(null);
  
  // 创建隐藏的文件输入元素
  const createFileInput = () => {
    if (!fileInputRef.value) {
      const input = document.createElement('input');
      input.type = 'file';
      input.style.display = 'none';
      input.multiple = options.multiple ?? true;
      input.accept = options.accept ?? '*/*';
      
      input.addEventListener('change', (e) => {
        const files = Array.from(e.target.files);
        if (files.length > 0 && options.onFileSelect) {
          options.onFileSelect(files);
        }
        // 清空input，允许重复选择同一文件
        input.value = '';
      });
      
      document.body.appendChild(input);
      fileInputRef.value = input;
    }
    return fileInputRef.value;
  };
  
  // 主动触发文件选择
  const triggerUpload = () => {
    const input = createFileInput();
    input.click();
  };
  
  // 清理函数
  const cleanup = () => {
    if (fileInputRef.value) {
      document.body.removeChild(fileInputRef.value);
      fileInputRef.value = null;
    }
  };
  
  return {
    triggerUpload,
    cleanup
  };
}