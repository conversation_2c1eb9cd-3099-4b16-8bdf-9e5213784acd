<!DOCTYPE html>

<html>
<head>
  <title>backbone.localStorage.js</title>
  <meta http-equiv="content-type" content="text/html; charset=UTF-8">
  <meta name="viewport" content="width=device-width, target-densitydpi=160dpi, initial-scale=1.0; maximum-scale=1.0; user-scalable=0;">
  <link rel="stylesheet" media="all" href="../docco.css" />
</head>
<body>
  <div id="container">
    <div id="background"></div>
    
      <ul id="jump_to">
        <li>
          <a class="large" href="javascript:void(0);">Jump To &hellip;</a>
          <a class="small" href="javascript:void(0);">+</a>
          <div id="jump_wrapper">
          <div id="jump_page_wrapper">
            <div id="jump_page">
              
                
                <a class="source" href="backbone.localStorage.html">
                  examples/backbone.localStorage.js
                </a>
              
                
                <a class="source" href="todos/todos.html">
                  examples/todos/todos.js
                </a>
              
            </div>
          </div>
        </li>
      </ul>
    
    <ul class="sections">
        
          <li id="title">
              <div class="annotation">
                  <h1>backbone.localStorage.js</h1>
              </div>
          </li>
        
        
        
        <li id="section-1">
            <div class="annotation">
              
              <div class="sswrap ">
                <a class="ss" href="#section-1">&#x00a7;</a>
              </div>
              
            </div>
            
            <div class="content"><div class='highlight'><pre><span class="hljs-comment">/**
 * Backbone localStorage Adapter
 * Version 1.1.0
 *
 * https://github.com/jeromegn/Backbone.localStorage
 */</span>
(<span class="hljs-keyword">function</span> (<span class="hljs-params">root, factory</span>) {
   <span class="hljs-keyword">if</span> (<span class="hljs-keyword">typeof</span> define === <span class="hljs-string">&quot;function&quot;</span> &amp;&amp; define.<span class="hljs-property">amd</span>) {</pre></div></div>
            
        </li>
        
        
        <li id="section-2">
            <div class="annotation">
              
              <div class="sswrap ">
                <a class="ss" href="#section-2">&#x00a7;</a>
              </div>
              <p>AMD. Register as an anonymous module.</p>

            </div>
            
            <div class="content"><div class='highlight'><pre>      <span class="hljs-title function_">define</span>([<span class="hljs-string">&quot;underscore&quot;</span>,<span class="hljs-string">&quot;backbone&quot;</span>], <span class="hljs-keyword">function</span>(<span class="hljs-params">_, Backbone</span>) {</pre></div></div>
            
        </li>
        
        
        <li id="section-3">
            <div class="annotation">
              
              <div class="sswrap ">
                <a class="ss" href="#section-3">&#x00a7;</a>
              </div>
              <p>Use global variables if the locals are undefined.</p>

            </div>
            
            <div class="content"><div class='highlight'><pre>        <span class="hljs-keyword">return</span> <span class="hljs-title function_">factory</span>(_ || root.<span class="hljs-property">_</span>, <span class="hljs-title class_">Backbone</span> || root.<span class="hljs-property">Backbone</span>);
      });
   } <span class="hljs-keyword">else</span> {</pre></div></div>
            
        </li>
        
        
        <li id="section-4">
            <div class="annotation">
              
              <div class="sswrap ">
                <a class="ss" href="#section-4">&#x00a7;</a>
              </div>
              <p>RequireJS isn’t being used. Assume underscore and backbone are loaded in script tags</p>

            </div>
            
            <div class="content"><div class='highlight'><pre>      <span class="hljs-title function_">factory</span>(_, <span class="hljs-title class_">Backbone</span>);
   }
}(<span class="hljs-variable language_">this</span>, <span class="hljs-keyword">function</span>(<span class="hljs-params">_, Backbone</span>) {</pre></div></div>
            
        </li>
        
        
        <li id="section-5">
            <div class="annotation">
              
              <div class="sswrap ">
                <a class="ss" href="#section-5">&#x00a7;</a>
              </div>
              <p>A simple module to replace <code>Backbone.sync</code> with <em>localStorage</em>-based
persistence. Models are given GUIDS, and saved into a JSON object. Simple
as that.</p>

            </div>
            
        </li>
        
        
        <li id="section-6">
            <div class="annotation">
              
              <div class="sswrap ">
                <a class="ss" href="#section-6">&#x00a7;</a>
              </div>
              <p>Hold reference to Underscore.js and Backbone.js in the closure in order
to make things work even if they are removed from the global namespace</p>

            </div>
            
        </li>
        
        
        <li id="section-7">
            <div class="annotation">
              
              <div class="sswrap ">
                <a class="ss" href="#section-7">&#x00a7;</a>
              </div>
              <p>Generate four random hex digits.</p>

            </div>
            
            <div class="content"><div class='highlight'><pre><span class="hljs-keyword">function</span> <span class="hljs-title function_">S4</span>(<span class="hljs-params"></span>) {
   <span class="hljs-keyword">return</span> (((<span class="hljs-number">1</span>+<span class="hljs-title class_">Math</span>.<span class="hljs-title function_">random</span>())*<span class="hljs-number">0x10000</span>)|<span class="hljs-number">0</span>).<span class="hljs-title function_">toString</span>(<span class="hljs-number">16</span>).<span class="hljs-title function_">substring</span>(<span class="hljs-number">1</span>);
};</pre></div></div>
            
        </li>
        
        
        <li id="section-8">
            <div class="annotation">
              
              <div class="sswrap ">
                <a class="ss" href="#section-8">&#x00a7;</a>
              </div>
              <p>Generate a pseudo-GUID by concatenating random hexadecimal.</p>

            </div>
            
            <div class="content"><div class='highlight'><pre><span class="hljs-keyword">function</span> <span class="hljs-title function_">guid</span>(<span class="hljs-params"></span>) {
   <span class="hljs-keyword">return</span> (<span class="hljs-title function_">S4</span>()+<span class="hljs-title function_">S4</span>()+<span class="hljs-string">&quot;-&quot;</span>+<span class="hljs-title function_">S4</span>()+<span class="hljs-string">&quot;-&quot;</span>+<span class="hljs-title function_">S4</span>()+<span class="hljs-string">&quot;-&quot;</span>+<span class="hljs-title function_">S4</span>()+<span class="hljs-string">&quot;-&quot;</span>+<span class="hljs-title function_">S4</span>()+<span class="hljs-title function_">S4</span>()+<span class="hljs-title function_">S4</span>());
};</pre></div></div>
            
        </li>
        
        
        <li id="section-9">
            <div class="annotation">
              
              <div class="sswrap ">
                <a class="ss" href="#section-9">&#x00a7;</a>
              </div>
              <p>Our Store is represented by a single JS object in <em>localStorage</em>. Create it
with a meaningful name, like the name you’d give a table.
window.Store is deprecated, use Backbone.LocalStorage instead</p>

            </div>
            
            <div class="content"><div class='highlight'><pre><span class="hljs-title class_">Backbone</span>.<span class="hljs-property">LocalStorage</span> = <span class="hljs-variable language_">window</span>.<span class="hljs-property">Store</span> = <span class="hljs-keyword">function</span>(<span class="hljs-params">name</span>) {
  <span class="hljs-variable language_">this</span>.<span class="hljs-property">name</span> = name;
  <span class="hljs-keyword">var</span> store = <span class="hljs-variable language_">this</span>.<span class="hljs-title function_">localStorage</span>().<span class="hljs-title function_">getItem</span>(<span class="hljs-variable language_">this</span>.<span class="hljs-property">name</span>);
  <span class="hljs-variable language_">this</span>.<span class="hljs-property">records</span> = (store &amp;&amp; store.<span class="hljs-title function_">split</span>(<span class="hljs-string">&quot;,&quot;</span>)) || [];
};

_.<span class="hljs-title function_">extend</span>(<span class="hljs-title class_">Backbone</span>.<span class="hljs-property">LocalStorage</span>.<span class="hljs-property"><span class="hljs-keyword">prototype</span></span>, {</pre></div></div>
            
        </li>
        
        
        <li id="section-10">
            <div class="annotation">
              
              <div class="sswrap ">
                <a class="ss" href="#section-10">&#x00a7;</a>
              </div>
              <p>Save the current state of the <strong>Store</strong> to <em>localStorage</em>.</p>

            </div>
            
            <div class="content"><div class='highlight'><pre>  <span class="hljs-attr">save</span>: <span class="hljs-keyword">function</span>(<span class="hljs-params"></span>) {
    <span class="hljs-variable language_">this</span>.<span class="hljs-title function_">localStorage</span>().<span class="hljs-title function_">setItem</span>(<span class="hljs-variable language_">this</span>.<span class="hljs-property">name</span>, <span class="hljs-variable language_">this</span>.<span class="hljs-property">records</span>.<span class="hljs-title function_">join</span>(<span class="hljs-string">&quot;,&quot;</span>));
  },</pre></div></div>
            
        </li>
        
        
        <li id="section-11">
            <div class="annotation">
              
              <div class="sswrap ">
                <a class="ss" href="#section-11">&#x00a7;</a>
              </div>
              <p>Add a model, giving it a (hopefully)-unique GUID, if it doesn’t already
have an id of it’s own.</p>

            </div>
            
            <div class="content"><div class='highlight'><pre>  <span class="hljs-attr">create</span>: <span class="hljs-keyword">function</span>(<span class="hljs-params">model</span>) {
    <span class="hljs-keyword">if</span> (!model.<span class="hljs-property">id</span>) {
      model.<span class="hljs-property">id</span> = <span class="hljs-title function_">guid</span>();
      model.<span class="hljs-title function_">set</span>(model.<span class="hljs-property">idAttribute</span>, model.<span class="hljs-property">id</span>);
    }
    <span class="hljs-variable language_">this</span>.<span class="hljs-title function_">localStorage</span>().<span class="hljs-title function_">setItem</span>(<span class="hljs-variable language_">this</span>.<span class="hljs-property">name</span>+<span class="hljs-string">&quot;-&quot;</span>+model.<span class="hljs-property">id</span>, <span class="hljs-title class_">JSON</span>.<span class="hljs-title function_">stringify</span>(model));
    <span class="hljs-variable language_">this</span>.<span class="hljs-property">records</span>.<span class="hljs-title function_">push</span>(model.<span class="hljs-property">id</span>.<span class="hljs-title function_">toString</span>());
    <span class="hljs-variable language_">this</span>.<span class="hljs-title function_">save</span>();
    <span class="hljs-keyword">return</span> <span class="hljs-variable language_">this</span>.<span class="hljs-title function_">find</span>(model);
  },</pre></div></div>
            
        </li>
        
        
        <li id="section-12">
            <div class="annotation">
              
              <div class="sswrap ">
                <a class="ss" href="#section-12">&#x00a7;</a>
              </div>
              <p>Update a model by replacing its copy in <code>this.data</code>.</p>

            </div>
            
            <div class="content"><div class='highlight'><pre>  <span class="hljs-attr">update</span>: <span class="hljs-keyword">function</span>(<span class="hljs-params">model</span>) {
    <span class="hljs-variable language_">this</span>.<span class="hljs-title function_">localStorage</span>().<span class="hljs-title function_">setItem</span>(<span class="hljs-variable language_">this</span>.<span class="hljs-property">name</span>+<span class="hljs-string">&quot;-&quot;</span>+model.<span class="hljs-property">id</span>, <span class="hljs-title class_">JSON</span>.<span class="hljs-title function_">stringify</span>(model));
    <span class="hljs-keyword">if</span> (!_.<span class="hljs-title function_">include</span>(<span class="hljs-variable language_">this</span>.<span class="hljs-property">records</span>, model.<span class="hljs-property">id</span>.<span class="hljs-title function_">toString</span>()))
      <span class="hljs-variable language_">this</span>.<span class="hljs-property">records</span>.<span class="hljs-title function_">push</span>(model.<span class="hljs-property">id</span>.<span class="hljs-title function_">toString</span>()); <span class="hljs-variable language_">this</span>.<span class="hljs-title function_">save</span>();
    <span class="hljs-keyword">return</span> <span class="hljs-variable language_">this</span>.<span class="hljs-title function_">find</span>(model);
  },</pre></div></div>
            
        </li>
        
        
        <li id="section-13">
            <div class="annotation">
              
              <div class="sswrap ">
                <a class="ss" href="#section-13">&#x00a7;</a>
              </div>
              <p>Retrieve a model from <code>this.data</code> by id.</p>

            </div>
            
            <div class="content"><div class='highlight'><pre>  <span class="hljs-attr">find</span>: <span class="hljs-keyword">function</span>(<span class="hljs-params">model</span>) {
    <span class="hljs-keyword">return</span> <span class="hljs-variable language_">this</span>.<span class="hljs-title function_">jsonData</span>(<span class="hljs-variable language_">this</span>.<span class="hljs-title function_">localStorage</span>().<span class="hljs-title function_">getItem</span>(<span class="hljs-variable language_">this</span>.<span class="hljs-property">name</span>+<span class="hljs-string">&quot;-&quot;</span>+model.<span class="hljs-property">id</span>));
  },</pre></div></div>
            
        </li>
        
        
        <li id="section-14">
            <div class="annotation">
              
              <div class="sswrap ">
                <a class="ss" href="#section-14">&#x00a7;</a>
              </div>
              <p>Return the array of all models currently in storage.</p>

            </div>
            
            <div class="content"><div class='highlight'><pre>  <span class="hljs-attr">findAll</span>: <span class="hljs-keyword">function</span>(<span class="hljs-params"></span>) {
    <span class="hljs-keyword">return</span> <span class="hljs-title function_">_</span>(<span class="hljs-variable language_">this</span>.<span class="hljs-property">records</span>).<span class="hljs-title function_">chain</span>()
      .<span class="hljs-title function_">map</span>(<span class="hljs-keyword">function</span>(<span class="hljs-params">id</span>){
        <span class="hljs-keyword">return</span> <span class="hljs-variable language_">this</span>.<span class="hljs-title function_">jsonData</span>(<span class="hljs-variable language_">this</span>.<span class="hljs-title function_">localStorage</span>().<span class="hljs-title function_">getItem</span>(<span class="hljs-variable language_">this</span>.<span class="hljs-property">name</span>+<span class="hljs-string">&quot;-&quot;</span>+id));
      }, <span class="hljs-variable language_">this</span>)
      .<span class="hljs-title function_">compact</span>()
      .<span class="hljs-title function_">value</span>();
  },</pre></div></div>
            
        </li>
        
        
        <li id="section-15">
            <div class="annotation">
              
              <div class="sswrap ">
                <a class="ss" href="#section-15">&#x00a7;</a>
              </div>
              <p>Delete a model from <code>this.data</code>, returning it.</p>

            </div>
            
            <div class="content"><div class='highlight'><pre>  <span class="hljs-attr">destroy</span>: <span class="hljs-keyword">function</span>(<span class="hljs-params">model</span>) {
    <span class="hljs-keyword">if</span> (model.<span class="hljs-title function_">isNew</span>())
      <span class="hljs-keyword">return</span> <span class="hljs-literal">false</span>
    <span class="hljs-variable language_">this</span>.<span class="hljs-title function_">localStorage</span>().<span class="hljs-title function_">removeItem</span>(<span class="hljs-variable language_">this</span>.<span class="hljs-property">name</span>+<span class="hljs-string">&quot;-&quot;</span>+model.<span class="hljs-property">id</span>);
    <span class="hljs-variable language_">this</span>.<span class="hljs-property">records</span> = _.<span class="hljs-title function_">reject</span>(<span class="hljs-variable language_">this</span>.<span class="hljs-property">records</span>, <span class="hljs-keyword">function</span>(<span class="hljs-params">id</span>){
      <span class="hljs-keyword">return</span> id === model.<span class="hljs-property">id</span>.<span class="hljs-title function_">toString</span>();
    });
    <span class="hljs-variable language_">this</span>.<span class="hljs-title function_">save</span>();
    <span class="hljs-keyword">return</span> model;
  },

  <span class="hljs-attr">localStorage</span>: <span class="hljs-keyword">function</span>(<span class="hljs-params"></span>) {
    <span class="hljs-keyword">return</span> <span class="hljs-variable language_">localStorage</span>;
  },</pre></div></div>
            
        </li>
        
        
        <li id="section-16">
            <div class="annotation">
              
              <div class="sswrap ">
                <a class="ss" href="#section-16">&#x00a7;</a>
              </div>
              <p>fix for “illegal access” error on Android when JSON.parse is passed null</p>

            </div>
            
            <div class="content"><div class='highlight'><pre>  <span class="hljs-attr">jsonData</span>: <span class="hljs-keyword">function</span> (<span class="hljs-params">data</span>) {
      <span class="hljs-keyword">return</span> data &amp;&amp; <span class="hljs-title class_">JSON</span>.<span class="hljs-title function_">parse</span>(data);
  }

});</pre></div></div>
            
        </li>
        
        
        <li id="section-17">
            <div class="annotation">
              
              <div class="sswrap ">
                <a class="ss" href="#section-17">&#x00a7;</a>
              </div>
              <p>localSync delegate to the model or collection’s
<em>localStorage</em> property, which should be an instance of <code>Store</code>.
window.Store.sync and Backbone.localSync is deprecated, use Backbone.LocalStorage.sync instead</p>

            </div>
            
            <div class="content"><div class='highlight'><pre><span class="hljs-title class_">Backbone</span>.<span class="hljs-property">LocalStorage</span>.<span class="hljs-property">sync</span> = <span class="hljs-variable language_">window</span>.<span class="hljs-property">Store</span>.<span class="hljs-property">sync</span> = <span class="hljs-title class_">Backbone</span>.<span class="hljs-property">localSync</span> = <span class="hljs-keyword">function</span>(<span class="hljs-params">method, model, options</span>) {
  <span class="hljs-keyword">var</span> store = model.<span class="hljs-property">localStorage</span> || model.<span class="hljs-property">collection</span>.<span class="hljs-property">localStorage</span>;

  <span class="hljs-keyword">var</span> resp, errorMessage, syncDfd = $.<span class="hljs-title class_">Deferred</span> &amp;&amp; $.<span class="hljs-title class_">Deferred</span>(); <span class="hljs-comment">//If $ is having Deferred - use it.</span>

  <span class="hljs-keyword">try</span> {

    <span class="hljs-keyword">switch</span> (method) {
      <span class="hljs-keyword">case</span> <span class="hljs-string">&quot;read&quot;</span>:
        resp = model.<span class="hljs-property">id</span> != <span class="hljs-literal">undefined</span> ? store.<span class="hljs-title function_">find</span>(model) : store.<span class="hljs-title function_">findAll</span>();
        <span class="hljs-keyword">break</span>;
      <span class="hljs-keyword">case</span> <span class="hljs-string">&quot;create&quot;</span>:
        resp = store.<span class="hljs-title function_">create</span>(model);
        <span class="hljs-keyword">break</span>;
      <span class="hljs-keyword">case</span> <span class="hljs-string">&quot;update&quot;</span>:
        resp = store.<span class="hljs-title function_">update</span>(model);
        <span class="hljs-keyword">break</span>;
      <span class="hljs-keyword">case</span> <span class="hljs-string">&quot;delete&quot;</span>:
        resp = store.<span class="hljs-title function_">destroy</span>(model);
        <span class="hljs-keyword">break</span>;
    }

  } <span class="hljs-keyword">catch</span>(error) {
    <span class="hljs-keyword">if</span> (error.<span class="hljs-property">code</span> === <span class="hljs-title class_">DOMException</span>.<span class="hljs-property">QUOTA_EXCEEDED_ERR</span> &amp;&amp; <span class="hljs-variable language_">window</span>.<span class="hljs-property">localStorage</span>.<span class="hljs-property">length</span> === <span class="hljs-number">0</span>)
      errorMessage = <span class="hljs-string">&quot;Private browsing is unsupported&quot;</span>;
    <span class="hljs-keyword">else</span>
      errorMessage = error.<span class="hljs-property">message</span>;
  }

  <span class="hljs-keyword">if</span> (resp) {
    model.<span class="hljs-title function_">trigger</span>(<span class="hljs-string">&quot;sync&quot;</span>, model, resp, options);
    <span class="hljs-keyword">if</span> (options &amp;&amp; options.<span class="hljs-property">success</span>)
      options.<span class="hljs-title function_">success</span>(resp);
    <span class="hljs-keyword">if</span> (syncDfd)
      syncDfd.<span class="hljs-title function_">resolve</span>(resp);

  } <span class="hljs-keyword">else</span> {
    errorMessage = errorMessage ? errorMessage
                                : <span class="hljs-string">&quot;Record Not Found&quot;</span>;

    <span class="hljs-keyword">if</span> (options &amp;&amp; options.<span class="hljs-property">error</span>)
      options.<span class="hljs-title function_">error</span>(errorMessage);
    <span class="hljs-keyword">if</span> (syncDfd)
      syncDfd.<span class="hljs-title function_">reject</span>(errorMessage);
  }</pre></div></div>
            
        </li>
        
        
        <li id="section-18">
            <div class="annotation">
              
              <div class="sswrap ">
                <a class="ss" href="#section-18">&#x00a7;</a>
              </div>
              <p>add compatibility with $.ajax
always execute callback for success and error</p>

            </div>
            
            <div class="content"><div class='highlight'><pre>  <span class="hljs-keyword">if</span> (options &amp;&amp; options.<span class="hljs-property">complete</span>) options.<span class="hljs-title function_">complete</span>(resp);

  <span class="hljs-keyword">return</span> syncDfd &amp;&amp; syncDfd.<span class="hljs-title function_">promise</span>();
};

<span class="hljs-title class_">Backbone</span>.<span class="hljs-property">ajaxSync</span> = <span class="hljs-title class_">Backbone</span>.<span class="hljs-property">sync</span>;

<span class="hljs-title class_">Backbone</span>.<span class="hljs-property">getSyncMethod</span> = <span class="hljs-keyword">function</span>(<span class="hljs-params">model</span>) {
  <span class="hljs-keyword">if</span>(model.<span class="hljs-property">localStorage</span> || (model.<span class="hljs-property">collection</span> &amp;&amp; model.<span class="hljs-property">collection</span>.<span class="hljs-property">localStorage</span>)) {
    <span class="hljs-keyword">return</span> <span class="hljs-title class_">Backbone</span>.<span class="hljs-property">localSync</span>;
  }

  <span class="hljs-keyword">return</span> <span class="hljs-title class_">Backbone</span>.<span class="hljs-property">ajaxSync</span>;
};</pre></div></div>
            
        </li>
        
        
        <li id="section-19">
            <div class="annotation">
              
              <div class="sswrap ">
                <a class="ss" href="#section-19">&#x00a7;</a>
              </div>
              <p>Override ‘Backbone.sync’ to default to localSync,
the original ‘Backbone.sync’ is still available in ‘Backbone.ajaxSync’</p>

            </div>
            
            <div class="content"><div class='highlight'><pre><span class="hljs-title class_">Backbone</span>.<span class="hljs-property">sync</span> = <span class="hljs-keyword">function</span>(<span class="hljs-params">method, model, options</span>) {
  <span class="hljs-keyword">return</span> <span class="hljs-title class_">Backbone</span>.<span class="hljs-title function_">getSyncMethod</span>(model).<span class="hljs-title function_">apply</span>(<span class="hljs-variable language_">this</span>, [method, model, options]);
};

<span class="hljs-keyword">return</span> <span class="hljs-title class_">Backbone</span>.<span class="hljs-property">LocalStorage</span>;
}));</pre></div></div>
            
        </li>
        
    </ul>
  </div>
</body>
</html>
