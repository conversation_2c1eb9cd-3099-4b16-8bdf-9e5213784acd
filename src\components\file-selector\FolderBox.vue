<template>
  <div
    class="file-card folder-card relative rounded-lg overflow-hidden transition-all duration-200"
    :class="{ 'selected': isSelected }"
    :data-type="folder.type"
    :style="{
      backgroundColor: themeStore.isDark ? '#2a2a2a' : '#fff',
      borderColor: isSelected ? themeColors.primary : themeColors.borderColor,
      boxShadow: isSelected ? `0 0 0 2px ${themeColors.primary}` : 'none'
    }"
    @click="handleClick($event)"
    @contextmenu="handleContextMenu($event)"
  >
    <!-- 多选框 -->
    <div
      v-if="isMultiSelectMode"
      class="absolute top-2 left-2 z-10"
      @click.stop="handleCheckboxClick"
    >
      <n-checkbox :checked="isSelected" />
    </div>

    <!-- 标星按钮 -->
    <div
      class="absolute top-2 right-2 z-10 star-button"
      @click.stop="$emit('toggle-starred', folder)"
    >
      <n-icon size="18" :color="folder.starred ? '#f59e0b' : themeColors.iconColor">
        <component :is="folder.starred ? StarSharp : StarOutline" />
      </n-icon>
    </div>

    <!-- 文件夹图标 -->
    <div class="file-icon-container flex items-center justify-center p-4">
      <div
        class="file-icon-wrapper folder-icon-wrapper rounded-lg p-3 transition-all duration-200"
        :style="getIconWrapperStyle()"
      >
        <n-icon
          size="48"
          :color="getFileColor()"
          class="folder-icon transition-transform duration-200"
        >
          <component :is="getFileIcon()" />
        </n-icon>

        <!-- 文件夹特殊装饰 -->
        <div class="folder-decoration">
          <div class="folder-badge" :style="{ backgroundColor: getFileColor() }">
            <n-icon size="12" color="white">
              <FolderOpenOutline />
            </n-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 文件夹信息 -->
    <div class="file-info p-3 pt-0 text-center">
      <div
        class="file-name folder-name font-medium truncate mb-1 transition-colors duration-200"
        :style="{ color: getFileColor() }"
      >
        {{ folder.name }}
      </div>
      <div class="file-meta flex justify-center items-center text-xs" :style="{ color: themeColors.textSecondary }">
        <span>{{ folder.size }}</span>
        <span class="mx-1" v-if="folder.size">•</span>
        <span>{{ folder.modified }}</span>
      </div>
    </div>

    <!-- 操作下拉菜单 -->
    <div class="absolute bottom-2 right-2 menu-button" @click.stop>
      <n-dropdown
        trigger="click"
        :options="processedDropdownOptions"
        @select="key => $emit('dropdown-select', key, folder)"
        @click.stop
      >
        <div>
          <n-button quaternary circle size="small">
            <template #icon>
              <n-icon>
                <EllipsisHorizontal />
              </n-icon>
            </template>
          </n-button>
        </div>
      </n-dropdown>
    </div>
  </div>
</template>

<script setup>
import { computed, h } from 'vue';
import { NIcon, NCheckbox, NButton, NDropdown } from 'naive-ui';
import { useThemeStore } from '../../stores/theme';
import {
  StarOutline,
  StarSharp,
  EllipsisHorizontal,
  FolderOutline,
  FolderOpenOutline
} from '@vicons/ionicons5';

const props = defineProps({
  folder: {
    type: Object,
    required: true
  },
  fileColors: {
    type: Object,
    required: true
  },
  dropdownOptions: {
    type: Array,
    default: () => []
  },
  isMultiSelectMode: {
    type: Boolean,
    default: false
  },
  isSelected: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['click', 'toggle-starred', 'dropdown-select', 'checkbox-click', 'context-menu']);

const themeStore = useThemeStore();
const themeColors = computed(() => themeStore.getThemeColors());

// 处理下拉菜单选项，为图标添加正确的渲染函数
const processedDropdownOptions = computed(() => {
  return props.dropdownOptions.map(option => {
    if (option.type === 'divider') {
      return option;
    }

    return {
      ...option,
      icon: option.icon ? () => h(NIcon, null, { default: () => h(option.icon) }) : undefined
    };
  });
});

// 获取文件夹图标
const getFileIcon = () => {
  return FolderOutline;
};

// 获取文件夹颜色
const getFileColor = () => {
  return props.fileColors.folder || props.fileColors.default;
};

// 获取图标包装器样式
const getIconWrapperStyle = () => {
  const baseStyle = {
    backgroundColor: themeStore.isDark ? '#3a3a3a' : '#f5f5f5'
  };

  return {
    ...baseStyle,
    background: themeStore.isDark
      ? `linear-gradient(135deg, ${getFileColor()}20, ${getFileColor()}10)`
      : `linear-gradient(135deg, ${getFileColor()}15, ${getFileColor()}08)`,
    border: `1px solid ${getFileColor()}30`
  };
};

// 处理点击事件
const handleClick = (event) => {
  emit('click', event, props.folder);
};

// 处理复选框点击
const handleCheckboxClick = () => {
  emit('checkbox-click', props.folder);
};

// 处理右键菜单
const handleContextMenu = (event) => {
  event.preventDefault();
  emit('context-menu', event, props.folder);
};
</script>

<style scoped>
.file-card {
  border: 1px solid;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  padding-left: 10px;
  padding-right: 10px;
}

/* 文件夹卡片特殊样式 */
.folder-card {
  background: v-bind('themeStore.isDark ? "linear-gradient(135deg, #1e293b, #0f172a)" : "linear-gradient(135deg, #f8fafc, #f1f5f9)"');
}

.folder-card:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 8px 25px v-bind('themeStore.isDark ? "rgba(59, 130, 246, 0.3)" : "rgba(59, 130, 246, 0.2)"');
}

/* 文件夹图标包装器 */
.folder-icon-wrapper {
  position: relative;
  overflow: visible;
}

.folder-icon-wrapper:hover {
  transform: scale(1.1);
}

/* 文件夹图标动画 */
.folder-icon {
  transition: all 0.3s ease;
}

.folder-card:hover .folder-icon {
  transform: rotate(-5deg) scale(1.1);
}

/* 文件夹装饰 */
.folder-decoration {
  position: absolute;
  top: -4px;
  right: -4px;
}

.folder-badge {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* 文件夹名称样式 */
.folder-name {
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.star-button,
.menu-button {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.file-card:hover .star-button,
.file-card:hover .menu-button,
.file-card.selected .star-button,
.file-card.selected .menu-button {
  opacity: 1;
}

.file-card.selected {
  border-width: 2px;
}

.file-icon-container {
  padding-top: 1.5rem;
}
</style>